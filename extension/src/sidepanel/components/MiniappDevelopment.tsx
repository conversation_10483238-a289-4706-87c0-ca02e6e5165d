import React, { useState } from 'react';
import { MiniApp } from '../../types/miniapp';
import { Tooltip } from 'antd';
import { X, Clock } from 'lucide-react';
import { useLiveQuery } from 'dexie-react-hooks';
import { ChatStatus } from '@the-agent/shared';

import { db, UserInfo } from '~/storages/indexdb';
import InputArea from './InputArea';

interface MiniappDevelopmentProps {
  miniapp: MiniApp;
  setCurrentMiniapp: (miniapp: MiniApp | null) => void;
  user: UserInfo | null;
}

const MiniappDevelopment = ({ miniapp, setCurrentMiniapp }: MiniappDevelopmentProps) => {
  const [prompt, setPrompt] = useState('');
  const [status, setStatus] = useState<ChatStatus>('idle');
  const [workflowMode, setWorkflowMode] = useState(false);
  const [showHistoricalVersions, setShowHistoricalVersions] = useState(false);

  // Get messages for this miniapp using its ID as conversation ID
  const messages = useLiveQuery(() => db.getMessagesByConversation(miniapp.id), [miniapp.id]);

  // Mock data for testing - add installation and history if not present
  const mockMiniapp: MiniApp = {
    ...miniapp,
    installation: miniapp.installation || {
      code: '2',
      changelogs: 'Latest version with bug fixes and new features',
      deployedAt: Date.now() - 86400000, // 1 day ago
    },
    history:
      miniapp.history.length > 0
        ? miniapp.history
        : [
            {
              code: '1',
              changelogs: 'Initial version with basic functionality',
              deployedAt: Date.now() - 172800000, // 2 days ago
            },
            {
              code: '2',
              changelogs: 'Added new features and improved performance',
              deployedAt: Date.now() - 86400000, // 1 day ago
            },
            {
              code: '3',
              changelogs: 'Bug fixes and UI improvements',
              deployedAt: Date.now() - 3600000, // 1 hour ago
            },
          ],
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    });
  };

  const handleBack = () => {
    if (showHistoricalVersions) {
      setShowHistoricalVersions(false);
    } else {
      setCurrentMiniapp(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || status !== 'idle') return;

    // TODO: Implement message sending logic
    console.log('Send message:', prompt);
    setPrompt('');
  };

  const handleAbort = () => {
    // TODO: Implement abort logic
    console.log('Abort');
    setStatus('idle');
  };

  return (
    <div style={{ position: 'fixed', inset: 0, zIndex: 50 }}>
      {/* MiniApp Development Container */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundColor: '#ffffff',
          boxShadow: '0 0 15px 0 rgba(0, 0, 0, 0.15)',
          overflow: 'hidden',
          zIndex: 10,
        }}
      >
        {/* Header */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 16px',
            height: '44px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
            {showHistoricalVersions ? 'Historical Versions' : `${mockMiniapp.name} Development`}
          </h2>
          <Tooltip title="Close" placement="bottom">
            <button
              onClick={handleBack}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>

        {/* Content */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            height: 'calc(100vh - 44px)',
          }}
        >
          {showHistoricalVersions ? (
            // TODO optimize
            /* Historical Versions View */
            <div
              style={{
                flex: 1,
                overflowY: 'auto',
                padding: '16px',
              }}
            >
              <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
                {mockMiniapp.history.map((version, index) => (
                  <div
                    key={index}
                    style={{
                      padding: '16px',
                      borderRadius: '12px',
                      border: '1px solid #e5e7eb',
                      backgroundColor: '#ffffff',
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                    }}
                    onMouseOver={e => {
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                      e.currentTarget.style.borderColor = '#d1d5db';
                    }}
                    onMouseOut={e => {
                      e.currentTarget.style.backgroundColor = '#ffffff';
                      e.currentTarget.style.borderColor = '#e5e7eb';
                    }}
                  >
                    <div
                      style={{
                        width: '40px',
                        height: '40px',
                        borderRadius: '8px',
                        backgroundColor: '#f3f4f6',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      <Clock size={20} color="#6b7280" />
                    </div>
                    <div style={{ flex: 1 }}>
                      <div
                        style={{
                          fontSize: '16px',
                          fontWeight: 600,
                          color: '#111827',
                          marginBottom: '4px',
                        }}
                      >
                        Version {mockMiniapp.history.length - index}
                      </div>
                      <div style={{ fontSize: '14px', color: '#6b7280' }}>
                        Generated on {formatDate(version.deployedAt)}
                      </div>
                    </div>
                    <div style={{ color: '#6b7280' }}>
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <polyline points="9,18 15,12 9,6"></polyline>
                      </svg>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ) : (
            <>
              {/* Versions Section - Show if miniapp is deployed */}
              {mockMiniapp.installation && (
                <div
                  style={{
                    padding: '16px',
                    backgroundColor: '#ffffff',
                  }}
                >
                  <div
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '12px',
                      marginBottom: '12px',
                    }}
                  >
                    <div
                      style={{
                        width: '48px',
                        height: '48px',
                        borderRadius: '12px',
                        backgroundColor: '#3b82f6',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: '#ffffff',
                        fontSize: '20px',
                        fontWeight: 600,
                      }}
                    >
                      {mockMiniapp.name.charAt(0).toUpperCase()}
                    </div>
                    <div style={{ flex: 1 }}>
                      <div
                        style={{
                          fontSize: '16px',
                          fontWeight: 600,
                          color: '#111827',
                          marginBottom: '2px',
                        }}
                      >
                        {mockMiniapp.name}
                      </div>
                      <div style={{ fontSize: '14px', color: '#6b7280', marginBottom: '2px' }}>
                        Version {mockMiniapp.installation!.code}
                      </div>
                      <div style={{ fontSize: '12px', color: '#6b7280' }}>
                        Installed on {formatDate(mockMiniapp.installation!.deployedAt)}
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => setShowHistoricalVersions(true)}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: '8px',
                      padding: '8px 12px',
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      backgroundColor: '#ffffff',
                      color: '#374151',
                      fontSize: '14px',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      width: '100%',
                      justifyContent: 'space-between',
                    }}
                    onMouseOver={e => {
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                      e.currentTarget.style.borderColor = '#d1d5db';
                    }}
                    onMouseOut={e => {
                      e.currentTarget.style.backgroundColor = '#ffffff';
                      e.currentTarget.style.borderColor = '#e5e7eb';
                    }}
                  >
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <Clock size={16} color="#6b7280" />
                      <span>View all versions</span>
                    </div>
                    <div style={{ color: '#6b7280' }}>
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                      >
                        <polyline points="9,18 15,12 9,6"></polyline>
                      </svg>
                    </div>
                  </button>
                </div>
              )}
              {/* Messages Area */}
              <div
                style={{
                  flex: 1,
                  overflowY: 'auto',
                  padding: '16px',
                }}
              >
                {messages && messages.length > 0 ? (
                  // Display messages
                  <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                    {messages.map(message => (
                      <div
                        key={message.id}
                        style={{
                          padding: '12px 16px',
                          borderRadius: '8px',
                          backgroundColor: message.role === 'user' ? '#f3f4f6' : '#ffffff',
                          border: '1px solid #e5e7eb',
                        }}
                      >
                        <div
                          style={{
                            fontSize: '12px',
                            color: '#6b7280',
                            marginBottom: '4px',
                            fontWeight: 500,
                          }}
                        >
                          {message.role === 'user' ? 'You' : 'Assistant'}
                        </div>
                        <div
                          style={{
                            fontSize: '14px',
                            color: '#111827',
                            lineHeight: 1.5,
                            whiteSpace: 'pre-wrap',
                          }}
                        >
                          {message.content}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  // Welcome message when no messages
                  <div
                    style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      justifyContent: 'center',
                      height: '100%',
                      gap: '16px',
                    }}
                  >
                    <div
                      style={{
                        textAlign: 'center',
                        maxWidth: '400px',
                      }}
                    >
                      <h3
                        style={{
                          fontSize: '24px',
                          fontWeight: 600,
                          color: '#111827',
                          marginBottom: '8px',
                        }}
                      >
                        Hello,
                      </h3>
                      <h4
                        style={{
                          fontSize: '20px',
                          fontWeight: 500,
                          color: '#111827',
                          marginBottom: '16px',
                        }}
                      >
                        I&apos;m the MiniApp Assistant
                      </h4>
                      <p
                        style={{
                          fontSize: '16px',
                          color: '#6b7280',
                          lineHeight: 1.5,
                        }}
                      >
                        Tell me what script you want to create
                      </p>
                    </div>
                  </div>
                )}
              </div>

              {/* Input Area */}
              <div style={{ padding: '16px 0' }}>
                <InputArea
                  prompt={prompt}
                  setPrompt={setPrompt}
                  onSubmit={handleSubmit}
                  status={status}
                  abort={handleAbort}
                  workflowMode={workflowMode}
                  onWorkflowModeChange={setWorkflowMode}
                />
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default MiniappDevelopment;
