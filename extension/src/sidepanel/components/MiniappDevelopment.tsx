import React, { useState } from 'react';
import { MiniApp } from '../../types/miniapp';
import { Tooltip } from 'antd';
import { X } from 'lucide-react';
import { useLiveQuery } from 'dexie-react-hooks';
import { ChatStatus } from '@the-agent/shared';

import { db, UserInfo } from '~/storages/indexdb';
import InputArea from './InputArea';

interface MiniappDevelopmentProps {
  miniapp: MiniApp;
  setCurrentMiniapp: (miniapp: MiniApp | null) => void;
  user: UserInfo | null;
}

const MiniappDevelopment = ({ miniapp, setCurrentMiniapp }: MiniappDevelopmentProps) => {
  const [prompt, setPrompt] = useState('');
  const [status, setStatus] = useState<ChatStatus>('idle');
  const [workflowMode, setWorkflowMode] = useState(false);

  // Get messages for this miniapp using its ID as conversation ID
  const messages = useLiveQuery(() => db.getMessagesByConversation(miniapp.id), [miniapp.id]);

  const handleBack = () => {
    setCurrentMiniapp(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || status !== 'idle') return;

    // TODO: Implement message sending logic
    console.log('Send message:', prompt);
    setPrompt('');
  };

  const handleAbort = () => {
    // TODO: Implement abort logic
    console.log('Abort');
    setStatus('idle');
  };

  return (
    <div style={{ position: 'fixed', inset: 0, zIndex: 50 }}>
      {/* MiniApp Development Container */}
      <div
        style={{
          position: 'absolute',
          inset: 0,
          backgroundColor: '#ffffff',
          boxShadow: '0 0 15px 0 rgba(0, 0, 0, 0.15)',
          overflow: 'hidden',
          zIndex: 10,
        }}
      >
        {/* Header */}
        <div
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            padding: '0 16px',
            height: '44px',
            borderBottom: '1px solid #f0f0f0',
          }}
        >
          <h2 style={{ fontSize: '15px', fontWeight: 600, color: '#111827' }}>
            {miniapp.name} Development
          </h2>
          <Tooltip title="Close" placement="bottom">
            <button
              onClick={handleBack}
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '32px',
                height: '32px',
                borderRadius: '50%',
                backgroundColor: 'transparent',
                border: 'none',
                color: '#6b7280',
                cursor: 'pointer',
                transition: 'background 0.2s',
              }}
              onMouseOver={e => {
                e.currentTarget.style.backgroundColor = '#E5E7EB';
              }}
              onMouseOut={e => {
                e.currentTarget.style.backgroundColor = 'transparent';
              }}
            >
              <X color="#374151" size={20} />
            </button>
          </Tooltip>
        </div>

        {/* Content */}
        <div
          style={{
            display: 'flex',
            flexDirection: 'column',
            height: 'calc(100vh - 44px)',
          }}
        >
          {/* TODO show versions on top, if the miniapp deployed `miniapp.installation` */}
          {/* Messages Area */}
          <div
            style={{
              flex: 1,
              overflowY: 'auto',
              padding: '16px',
            }}
          >
            {messages && messages.length > 0 ? (
              // Display messages
              <div style={{ display: 'flex', flexDirection: 'column', gap: '16px' }}>
                {messages.map(message => (
                  <div
                    key={message.id}
                    style={{
                      padding: '12px 16px',
                      borderRadius: '8px',
                      backgroundColor: message.role === 'user' ? '#f3f4f6' : '#ffffff',
                      border: '1px solid #e5e7eb',
                    }}
                  >
                    <div
                      style={{
                        fontSize: '12px',
                        color: '#6b7280',
                        marginBottom: '4px',
                        fontWeight: 500,
                      }}
                    >
                      {message.role === 'user' ? 'You' : 'Assistant'}
                    </div>
                    <div
                      style={{
                        fontSize: '14px',
                        color: '#111827',
                        lineHeight: 1.5,
                        whiteSpace: 'pre-wrap',
                      }}
                    >
                      {message.content}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              // Welcome message when no messages
              <div
                style={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  justifyContent: 'center',
                  height: '100%',
                  gap: '16px',
                }}
              >
                <div
                  style={{
                    textAlign: 'center',
                    maxWidth: '400px',
                  }}
                >
                  <h3
                    style={{
                      fontSize: '24px',
                      fontWeight: 600,
                      color: '#111827',
                      marginBottom: '8px',
                    }}
                  >
                    Hello,
                  </h3>
                  <h4
                    style={{
                      fontSize: '20px',
                      fontWeight: 500,
                      color: '#111827',
                      marginBottom: '16px',
                    }}
                  >
                    I&apos;m the MiniApp Assistant
                  </h4>
                  <p
                    style={{
                      fontSize: '16px',
                      color: '#6b7280',
                      lineHeight: 1.5,
                    }}
                  >
                    Tell me what script you want to create
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Input Area */}
          <div style={{ padding: '16px 0' }}>
            <InputArea
              prompt={prompt}
              setPrompt={setPrompt}
              onSubmit={handleSubmit}
              status={status}
              abort={handleAbort}
              workflowMode={workflowMode}
              onWorkflowModeChange={setWorkflowMode}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default MiniappDevelopment;
