import React from 'react';
import newminiappIcon from '~/assets/icons/newminiapp.svg';

interface MiniappEmptyStateProps {
  onNewProject: () => void;
}

const MiniappEmptyState: React.FC<MiniappEmptyStateProps> = ({ onNewProject }) => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: 'calc(100vh - 200px)',
        gap: '24px',
      }}
    >
      <p style={{ fontSize: '14px', color: '#6b7280', textAlign: 'center' }}>
        Sorry, you currently do not have a MiniApp.
      </p>
      <button
        onClick={onNewProject}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '8px',
          padding: '12px 24px',
          borderRadius: '8px',
          border: '1px solid #d1d5db',
          backgroundColor: '#ffffff',
          color: '#374151',
          fontSize: '14px',
          fontWeight: 500,
          cursor: 'pointer',
          transition: 'all 0.2s',
          minWidth: '200px',
        }}
        onMouseOver={e => {
          e.currentTarget.style.backgroundColor = '#f9fafb';
          e.currentTarget.style.borderColor = '#9ca3af';
        }}
        onMouseOut={e => {
          e.currentTarget.style.backgroundColor = '#ffffff';
          e.currentTarget.style.borderColor = '#d1d5db';
        }}
      >
        <img src={newminiappIcon} alt="New Project" style={{ width: 20, height: 20 }} />
        New Project
      </button>
    </div>
  );
};

export default MiniappEmptyState;
