import React, { useState, useEffect, useRef, useCallback } from 'react';
import '../style.css';
import {
  Header,
  Message,
  InputArea,
  ConversationList,
  LoginModal,
  Thinking,
  Home,
  TopBanner,
  MiniappIndex,
  MiniappDevelopment,
} from './components';

import {
  selectConversation as selectConv,
  deleteConversation as deleteConv,
  syncConversations,
  createNewConversation,
} from '../services/conversation';
import { db, UserInfo } from '~/storages/indexdb';
import { useLiveQuery } from 'dexie-react-hooks';
import { APIError, Message as SMessage, ChatStatus } from '@the-agent/shared';
import { ApiKey } from '~/types';
import { MiniApp } from '~/types/miniapp';
import { API_KEY_TAG } from '~/storages/cache';
import { getUserInfo } from '~/services/user';
import { parseApiKey, isEqualApiKey } from '~/services/api/key';
import StarIcon from '~/assets/icons/star.svg';
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from '~/chat/handler';
import { createApiClient } from '../services/api/client';
import { message as antdMessage } from 'antd';
import { useLanguage } from '../utils/i18n';
import welcomeImg from '~/assets/imgs/prompt.png';

const SidepanelContent = () => {
  const { getMessage } = useLanguage();

  // Prompt templates that users can click on to quickly fill the input
  const getPromptTemplates = () => [
    getMessage('summarizeElonTweet'),
    getMessage('postTweet'),
    getMessage('searchLinkedIn'),
  ];
  const [apiKey, setApiKey] = useState<ApiKey | null>(null);
  const [prompt, setPrompt] = useState('');
  const [showConversationList, setShowConversationList] = useState(false);
  const [showMiniapp, setShowMiniapp] = useState(false);
  const [currentMiniapp, setCurrentMiniapp] = useState<MiniApp | null>(null);
  const [status, setStatus] = useState<ChatStatus>('uninitialized');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [chatHandler, setChatHandler] = useState<ChatHandler | null>(null);
  const [loginModalOpen, setLoginModalOpen] = useState(false);
  const [showSwitch, setShowSwitch] = useState(false);
  const [currentUser, setCurrentUser] = useState<UserInfo | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [hasBanner, setHasBanner] = useState(false);
  const [workflowMode, setWorkflowMode] = useState(false);

  const conversations = useLiveQuery(
    () => (currentUser?.id ? db.getAllConversations(currentUser.id) : undefined),
    [currentUser?.id]
  );

  const currentConversationId = conversations?.length ? conversations[0].id : -1;

  const messages = useLiveQuery(
    () => (currentConversationId !== -1 ? db.getMessagesByConversation(currentConversationId) : []),
    [currentConversationId]
  );

  const lastMessageVersion =
    useLiveQuery(() => {
      if (currentConversationId === -1) return 0;
      return db.getLastMessageVersion(currentConversationId);
    }, [currentConversationId]) ?? 0;

  useEffect(() => {
    if (status === 'uninitialized' || !currentUser?.id) {
      return;
    }
    if (conversations?.length === 0) {
      createNewConversation(currentUser.id);
    }
  }, [conversations?.length, currentUser?.id]);

  const handleApiError = useCallback(
    (error: unknown) => {
      if (error instanceof APIError) {
        if (error.status === 401 || error.status === 403) {
          setApiKey(
            apiKey
              ? {
                  key: apiKey.key,
                  enabled: false,
                }
              : null
          );
          db.saveErrorMessage(currentConversationId, getMessage('apiKeyDisabled'));
          setLoginModalOpen(true);
          return;
        } else if (error.status === 402) {
          db.saveErrorMessage(currentConversationId, getMessage('insufficientCredits'));
          return;
        }
      }
      let message = getMessage('errorOccurred');
      if (error instanceof Error) {
        message = error.message;
      } else if (typeof error === 'string') {
        message = error;
      }
      db.saveErrorMessage(currentConversationId, message);
    },
    [currentConversationId, apiKey, setApiKey]
  );

  const initializeUserAndData = useCallback(
    async (apiKeyToUse: ApiKey) => {
      try {
        if (!apiKeyToUse.enabled) {
          throw new Error(getMessage('apiKeyDisabled'));
        }

        const existingUser = await db.getUserByApiKey(apiKeyToUse.key);
        if (existingUser) {
          await syncConversations(existingUser.id);
          setCurrentUser(existingUser);
          setLoginModalOpen(false);
          return;
        }

        const userInfo = await getUserInfo(apiKeyToUse);
        await db.initModels(userInfo.id);
        await db.saveOrUpdateUser(userInfo);
        await syncConversations(userInfo.id);
        setCurrentUser(userInfo);
        setLoginModalOpen(false);
      } catch (error) {
        handleApiError(error);
      }
    },
    [handleApiError]
  );

  useEffect(() => {
    const listener = async (
      changes: { [key: string]: chrome.storage.StorageChange },
      area: string
    ) => {
      // Only respond to external changes, not our own writes
      if (area === 'local' && changes[API_KEY_TAG]) {
        const oldApiKey = parseApiKey(changes[API_KEY_TAG].oldValue);
        const newApiKey = parseApiKey(changes[API_KEY_TAG].newValue);

        // Skip if no actual change
        if (!isEqualApiKey(oldApiKey, newApiKey)) {
          setApiKey(newApiKey);
          if (newApiKey?.enabled) {
            setStatus('uninitialized');
            setShowSwitch(true);
            await initializeUserAndData(newApiKey);
            setStatus('idle');
          } else {
            setStatus('uninitialized');
            setLoginModalOpen(true);
          }
        }
      }
    };
    chrome.storage.onChanged.addListener(listener);
    return () => chrome.storage.onChanged.removeListener(listener);
  }, [setApiKey, setShowSwitch, initializeUserAndData, setLoginModalOpen]);

  useEffect(() => {
    const handleMessages = (request: { name: string; text?: string }) => {
      if (request.name === 'selected-text' && request.text) {
        setPrompt(request.text);
      }
      if (request.name === 'focus-input') {
        const inputElement = document.querySelector('textarea');
        inputElement?.focus();
      }
      if (request.name === 'api-key-missing') {
        setLoginModalOpen(true);
      }
    };
    chrome.runtime.onMessage.addListener(handleMessages);
    return () => chrome.runtime.onMessage.removeListener(handleMessages);
  }, [setPrompt, setLoginModalOpen]);

  useEffect(() => {
    const handler = () => {
      setLoginModalOpen(true);
    };
    window.addEventListener('SHOW_LOGIN_MODAL', handler);
    return () => window.removeEventListener('SHOW_LOGIN_MODAL', handler);
  }, [setLoginModalOpen]);

  useEffect(() => {
    if (apiKey?.enabled && currentConversationId !== -1) {
      setChatHandler(
        new ChatHandler({
          apiKey: apiKey,
          currentConversationId,
          workflowMode: workflowMode,
          onError: handleApiError,
          onStatusChange: (status: ChatStatus) => {
            setStatus(status);
          },
          onMessageUpdate: async (message: SMessage) => {
            await db.saveMessage(message);
          },
        })
      );
    }
  }, [apiKey, currentConversationId, workflowMode, handleApiError]);

  // 自动滚动到底部
  useEffect(() => {
    const scrollToBottom = () => {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    };
    scrollToBottom();
  }, [messages?.length, lastMessageVersion]);

  // 事件处理函数
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim() || !chatHandler) return;

    const currentPrompt = prompt.trim();
    setPrompt('');
    await chatHandler.handleSubmit(currentPrompt);
  };

  const abort = useCallback(() => {
    chatHandler?.abort();
  }, [chatHandler]);

  const toggleConversationList = async (value?: boolean) => {
    const willShow = value !== undefined ? value : !showConversationList;
    setShowConversationList(willShow);
  };

  const handleSelectConversation = async (id: number) => {
    if (id === -1) {
      await handleCreateNewConversation();
      return;
    }

    try {
      await selectConv(id);
    } catch (error) {
      handleApiError(error);
    }
  };

  const handleDeleteConversation = async (id: number, e: React.MouseEvent) => {
    e.stopPropagation();
    try {
      await deleteConv(id);
    } catch (error) {
      handleApiError(error);
    }
  };

  const handleCreateNewConversation = async () => {
    try {
      if (currentUser?.id) {
        await createNewConversation(currentUser.id);
      }
    } catch (error) {
      handleApiError(error);
    }
  };

  const shouldShowSaveToSiteButton = () => {
    if (status !== 'idle') return false;
    if (!messages || messages.length === 0) return false;
    const lastMessage = messages[messages.length - 1];
    if (lastMessage.role !== 'assistant') return false;
    if (!currentUser?.permission?.can_save_site_message) return false;

    const hasUserOrToolMessage = messages.some(msg => msg.role === 'user' || msg.role === 'tool');
    if (!hasUserOrToolMessage) return false;

    return true;
  };

  const handleSaveToSite = async () => {
    if (!messages || isSaving) return;

    setIsSaving(true);
    try {
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      if (!activeTab?.url || activeTab.url.startsWith('chrome://')) {
        console.warn('Cannot save site message: invalid tab URL');
        antdMessage.warning('Cannot save for the current site.');
        return;
      }

      const messagesToProcess = messages.filter(msg => {
        if (msg.role === 'user') return true;
        if (msg.role === 'assistant') return true;
        if (msg.role === 'tool') {
          return (
            msg.tool_calls &&
            msg.tool_calls.length > 0 &&
            msg.tool_calls[0].result?.success === true
          );
        }
        return false;
      });

      if (messagesToProcess.length === 0) {
        console.log('No messages to process for FAQ generation.');
        antdMessage.info('No messages to save.');
        return;
      }

      // transform to new memory API format
      const hostname = new URL(activeTab.url).hostname;

      const memoryData = {
        messages,
        config: {
          filters: {
            hostname,
          },
          metadata: {
            hostname,
            memoryType: 'site',
          },
        },
      };

      const client = await createApiClient();

      await client.addMemory(memoryData);

      console.log('Site messages saved successfully');
      antdMessage.success('Saved to site memory successfully!');
    } catch (error) {
      console.error('Failed to save site messages:', error);
      antdMessage.error('Failed to save to site memory.');
      handleApiError(error);
    } finally {
      setIsSaving(false);
    }
  };

  // Monitor TopBanner visibility state
  useEffect(() => {
    const checkBannerState = () => {
      chrome.storage.local.get(['mysta-banner-closed'], result => {
        setHasBanner(!result['mysta-banner-closed']);
      });
    };

    // Initial check
    checkBannerState();

    // Listen for storage changes
    const handleStorageChange = (changes: { [key: string]: chrome.storage.StorageChange }) => {
      if (changes['mysta-banner-closed']) {
        setHasBanner(!changes['mysta-banner-closed'].newValue);
      }
    };

    chrome.storage.onChanged.addListener(handleStorageChange);

    return () => {
      chrome.storage.onChanged.removeListener(handleStorageChange);
    };
  }, []);

  // Initialize state from storage - run only once on mount
  useEffect(() => {
    const initializeFromStorage = async () => {
      chrome.storage.local.get([API_KEY_TAG], async result => {
        const storedApiKey = parseApiKey(result[API_KEY_TAG]);
        if (storedApiKey) {
          setApiKey(storedApiKey);
        }

        if (!storedApiKey?.enabled) {
          setLoginModalOpen(true);
        } else {
          await initializeUserAndData(storedApiKey);
        }
      });
    };

    // Only run this effect once on mount
    if (status === 'uninitialized') {
      initializeFromStorage();
      setStatus('idle');
    }
  }, []); // Empty dependency array - only run once

  // Debounced storage updates for apiKey
  useEffect(() => {
    if (!apiKey) return;

    const timer = setTimeout(() => {
      chrome.storage.local.set({ [API_KEY_TAG]: apiKey });
    }, 1000);

    return () => clearTimeout(timer);
  }, [apiKey]);

  if (!currentUser) {
    return <Home />;
  }

  return (
    <div
      style={{
        height: '100vh',
        width: '100%',
        position: 'relative',
        overflow: 'hidden',
      }}
    >
      {/* Header */}
      <div
        style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          zIndex: 10,
        }}
      >
        <Header
          createNewConversation={handleCreateNewConversation}
          setShowConversationList={() => toggleConversationList()}
          setShowMiniapp={(value?: boolean) => setShowMiniapp(value ?? true)}
          user={currentUser}
        />
      </div>

      {/* Top Banner */}
      <TopBanner />

      {/* Messages Area */}
      <div
        style={{
          position: 'absolute',
          top: hasBanner ? '84px' : '44px', // 44px (header) + 40px (banner) = 84px
          bottom: '90px',
          left: 0,
          right: 0,
          overflowY: 'auto',
          overflowX: 'hidden',
          backgroundColor: '#FFFFFF',
        }}
      >
        <div className="max-w-3xl mx-auto p-4">
          {(messages?.length ?? 0) === 0 ? (
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                padding: '0px 24px',
                minHeight: '100%',
              }}
            >
              <div
                style={{
                  maxWidth: '480px',
                  textAlign: 'center',
                }}
              >
                {/* Welcome image */}
                <img src={welcomeImg} alt="Welcome" style={{ maxWidth: '100%' }} />
                {/* Welcome text */}
                <p
                  style={{
                    fontSize: '25px',
                    fontWeight: '600',
                    color: '#374151',
                    marginBottom: '0px',
                    marginTop: '-30px',
                  }}
                >
                  {getMessage('askAnything').split('\n')[0]}
                  <br />
                  {getMessage('askAnything').split('\n')[1]}
                </p>
                <p
                  style={{
                    fontSize: '16px',
                    color: '#6b7280',
                    lineHeight: '1.6',
                    marginBottom: !apiKey ? '32px' : '0',
                  }}
                >
                  {getMessage('startTyping')}
                </p>
                {/* Prompt templates */}
                {apiKey && (
                  <div style={{ marginTop: '20px' }}>
                    {getPromptTemplates().map((template: string, index: number) => (
                      <div
                        key={index}
                        onClick={() => {
                          if (chatHandler) {
                            chatHandler.handleSubmit(template);
                          }
                        }}
                        style={{
                          display: 'flex',
                          alignItems: 'center',
                          padding: '16px',
                          marginBottom: '12px',
                          borderRadius: '12px',
                          border: '1px solid #e5e7eb',
                          backgroundColor: '#f9fafb',
                          cursor: 'pointer',
                          transition: 'all 0.2s ease',
                          boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                        }}
                        onMouseOver={e => {
                          e.currentTarget.style.backgroundColor = '#f3f4f6';
                          e.currentTarget.style.borderColor = '#d1d5db';
                        }}
                        onMouseOut={e => {
                          e.currentTarget.style.backgroundColor = '#f9fafb';
                          e.currentTarget.style.borderColor = '#e5e7eb';
                        }}
                      >
                        <div
                          style={{
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            width: '24px',
                            height: '24px',
                            marginRight: '12px',
                            flexShrink: 0,
                          }}
                        >
                          <img src={StarIcon} alt="star" />
                        </div>
                        <div style={{ textAlign: 'left', fontSize: '14px', color: '#4b5563' }}>
                          {template}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
                {!apiKey && (
                  <p
                    style={{
                      fontSize: '14px',
                      color: '#9ca3af',
                      maxWidth: '400px',
                      lineHeight: '1.5',
                    }}
                  >
                    {getMessage('apiKeyRequired')}
                  </p>
                )}
              </div>
            </div>
          ) : (
            <div style={{ paddingBottom: '32px' }}>
              {messages?.map((message, index) => {
                const isLast =
                  (index === messages.length - 1 && message.role === 'assistant') ||
                  message.status === 'error';

                return (
                  <Message
                    key={`${message.id}-${message.version}-${isLast}-${status}`}
                    message={message}
                    isLatestResponse={isLast}
                    status={status}
                    workflowMode={workflowMode}
                    taskId="root"
                  />
                );
              })}
              {status === 'running' && (
                <div style={{ padding: '16px 0', textAlign: 'center' }}>
                  <Thinking />
                </div>
              )}
              {shouldShowSaveToSiteButton() && (
                <div style={{ display: 'flex', justifyContent: 'center', marginTop: '16px' }}>
                  <button
                    onClick={handleSaveToSite}
                    disabled={isSaving}
                    style={{
                      display: 'flex',
                      alignItems: 'center',
                      padding: '8px 16px',
                      borderRadius: '8px',
                      border: '1px solid #e5e7eb',
                      backgroundColor: '#f9fafb',
                      cursor: isSaving ? 'not-allowed' : 'pointer',
                      transition: 'all 0.2s ease',
                      boxShadow: '0 1px 2px rgba(0, 0, 0, 0.05)',
                      color: '#4b5563',
                      fontSize: '14px',
                      fontWeight: 500,
                      opacity: isSaving ? 0.7 : 1,
                    }}
                    onMouseOver={e => {
                      if (isSaving) return;
                      e.currentTarget.style.backgroundColor = '#f3f4f6';
                      e.currentTarget.style.borderColor = '#d1d5db';
                    }}
                    onMouseOut={e => {
                      if (isSaving) return;
                      e.currentTarget.style.backgroundColor = '#f9fafb';
                      e.currentTarget.style.borderColor = '#e5e7eb';
                    }}
                    title={getMessage('saveToSiteMemory')}
                  >
                    {isSaving ? (
                      <>
                        <style>{`
                          @keyframes spin { 100% { transform: rotate(360deg); } }
                        `}</style>
                        <svg
                          style={{
                            width: '16px',
                            height: '16px',
                            marginRight: '8px',
                            animation: 'spin 1s linear infinite',
                          }}
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M21 12a9 9 0 1 1-6.219-8.56"
                          />
                        </svg>
                        {getMessage('loading')}
                      </>
                    ) : (
                      <>
                        <svg
                          style={{
                            width: '16px',
                            height: '16px',
                            marginRight: '8px',
                          }}
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
                          <polyline points="17,21 17,13 7,13 7,21" />
                          <polyline points="7,3 7,8 15,8" />
                        </svg>
                        {getMessage('saveToSiteMemoryTitle')}
                      </>
                    )}
                  </button>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>
      </div>

      {/* Input Area */}
      <div
        style={{
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: 'white',
          zIndex: 10,
        }}
      >
        <InputArea
          prompt={prompt}
          setPrompt={setPrompt}
          onSubmit={handleSubmit}
          status={status}
          abort={abort}
          workflowMode={workflowMode}
          onWorkflowModeChange={setWorkflowMode}
        />
      </div>

      {/* Conversation List */}
      {showConversationList && (
        <ConversationList
          conversations={conversations || []}
          currentConversationId={currentConversationId}
          selectConversation={handleSelectConversation}
          deleteConversation={handleDeleteConversation}
          setShowConversationList={(show: boolean) => toggleConversationList(show)}
        />
      )}

      {/* MiniApp Index */}
      {showMiniapp && !currentMiniapp && (
        <MiniappIndex
          setShowMiniapp={setShowMiniapp}
          setCurrentMiniapp={setCurrentMiniapp}
          user={currentUser}
        />
      )}

      {/* MiniApp Development */}
      {currentMiniapp && (
        <MiniappDevelopment
          miniapp={currentMiniapp}
          setCurrentMiniapp={setCurrentMiniapp}
          user={currentUser}
        />
      )}

      <LoginModal
        open={loginModalOpen}
        isSwitch={false}
        text={
          apiKey && !apiKey.enabled ? getMessage('enableApiKey') : getMessage('signInWithMysta')
        }
        currentUser={currentUser}
      />
      <LoginModal
        open={showSwitch}
        isSwitch={true}
        currentUser={currentUser}
        onClose={async () => {
          // First stop streaming and hide the switch modal
          if (chatHandler) {
            chatHandler.abort();
          }
          setShowSwitch(false);
        }}
      />
    </div>
  );
};

// Export for use in App.tsx
export { SidepanelContent };

// Main export with I18n Provider
import * as I18nModule from '../utils/i18n';

const App: React.FC = () => {
  const { I18nProvider } = I18nModule as any;

  return (
    <I18nProvider>
      <SidepanelContent />
    </I18nProvider>
  );
};

export default App;
