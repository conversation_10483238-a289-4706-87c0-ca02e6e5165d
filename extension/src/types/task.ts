import { Agent, ChatOptions, Message, Task } from '@the-agent/shared';

export type TaskStatus = 'completed' | 'error' | 'waiting' | 'pending';

export interface TaskState {
  parentTaskId: string | null;
  depth: number;
  started_at?: string;
  completed_at?: string;
  result?: TaskResult;
  runtimeInputs?: RuntimeInput[]; // raw inputs collected from previous Task output
  runtimeInputProcessed?: string;
  history: Message[];
}

export interface TaskResult {
  status: TaskStatus;
  output: string;
}

export interface RuntimeInput extends TaskResult {
  id: string;
}

export interface TaskWithState {
  task: Task;
  state: TaskState;
}

export interface TaskWithStatus {
  task: Task;
  status: TaskStatus;
}

export interface GlobalContext {
  tasks: Record<string, TaskWithState>;
  workflowId: string;
  processing: string[];
  processed: string[];
  chatOptions: ChatOptions;
  agents: {
    dispatcher?: Agent;
    foreach?: Agent;
  };
  aborted: boolean;
}
