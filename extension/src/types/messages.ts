export type ActionName =
  | 'ping'
  | 'execute-tool'
  | 'update-config'
  | 'selected-text'
  | 'focus-input'
  | 'api-key-missing'
  | 'inject-script'
  | 'load-scripts';
export interface RuntimeAction {
  name: ActionName;
  body?:
    | {
        name: string;
        arguments: object;
      }
    | {
        key: string;
        value: string;
      }
    | object;
}

export interface RuntimeActionResponse {
  success: boolean;
  message?: string;
  data?: unknown;
  error?: string;
}
