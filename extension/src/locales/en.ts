export const enMessages = {
  // Basic Extension Info
  extensionName: 'Mysta',
  extensionDescription: 'Mysta - Let AI Run the Web for You.',

  // Navigation and UI
  viewProfile: 'View Profile',
  conversations: 'Conversations',
  miniapp: 'Applications',
  newChat: 'New chat',
  allChats: 'All Chats',
  user: 'User',
  language: 'Language',
  english: 'English',
  chinese: '中文',
  settings: 'Settings',
  logout: 'Logout',
  workflowDetail: 'Task Detail',

  // Actions
  save: 'Save',
  cancel: 'Cancel',
  delete: 'Delete',
  close: 'Close',
  ok: 'OK',
  notNow: 'Not now',
  getStarted: 'Get started',

  // API Key Management
  setApiKey: 'Set API Key',
  enterApiKey: 'Enter $1 API Key',
  apiKeyDisabled: 'API key is disabled',
  goToGetApiKey: 'Go to get API Key',
  enableApiKey: 'Enable Your Mysta API Key',
  apiKeyRequired: 'You need to login to get API Key to use the full functionality',

  // Authentication
  signInWithMysta: 'Sign In with Mysta Web',
  mystaAccountDetected: 'Mysta Account Detected',

  // Chat Interface
  thinking: 'Thinking',
  typeMessage: 'Type a message...',
  send: 'Send',
  attachFile: 'Attach file',
  recordAudio: 'Record audio',
  workflowMode: 'Task Mode',
  normalMode: 'Chat Mode',

  // Error Messages
  insufficientCredits: 'Insufficient credits. Please add more credits to your account.',
  insufficientCreditsTip: 'Insufficient credits',
  rechargeTip: 'Add credits',
  errorOccurred: 'An error occurred. Please try again.',

  // TopBanner
  joinTelegramGroup: '🎉 Join our telegram group to get $5 credits!',
  cannotSaveForCurrentSite: 'Cannot save for the current site.',
  noMessagesToSave: 'No messages to save.',
  failedToSaveToSiteMemory: 'Failed to save to site memory.',

  // Success Messages
  savedToSiteMemorySuccessfully: 'Saved to site memory successfully!',

  // Tool Execution Status
  executing: 'Executing',
  executed: 'Executed',
  running: 'Running',
  error: 'Error',

  // Home Page
  letAiRunTheWeb: 'Let AI Run the Web for You',
  askAnything: 'Ask anything.\nAutomate Everything.',
  startTyping: 'Your AI agent is here to help.',
  privacyDisclaimer:
    'Mysta AI assistant may produce inaccurate information. Your data is kept private.',

  // Conversation Management
  deleteConversation: 'Delete Conversation',
  deleteConversationConfirm:
    'Are you sure you want to delete this conversation? This action cannot be undone.',
  newChatItem: 'New Chat',

  // Site Memory
  saveToSiteMemory: 'Save to site memory',
  saveToSiteMemoryTitle: 'Save to Site Memory',

  // Tooltips
  tooltipNewChat: 'New chat',
  tooltipClose: 'Close',
  tooltipDelete: 'Delete',
  tooltipConversations: 'Conversations',
  tooltipUser: 'User',
  tooltipSaveToSiteMemory: 'Save to site memory',

  // Prompt Templates
  summarizeElonTweet: "Summarize Elon Musk's latest tweet.",
  postTweet: "Post a tweet saying: 'mysta is awesome'.",
  searchLinkedIn: 'Search for MystaAI on my LinkedIn.',

  // Copy Functionality
  copyToClipboard: 'Copy to clipboard',
  copied: 'Copied!',

  // File Upload
  selectFile: 'Select file',
  uploadFile: 'Upload file',

  // Status
  idle: 'Idle',
  waiting: 'Waiting',
  processing: 'Processing',

  // Common Actions
  edit: 'Edit',
  share: 'Share',
  export: 'Export',
  import: 'Import',
  refresh: 'Refresh',
  retry: 'Retry',

  // Validation Messages
  fieldRequired: 'This field is required',
  invalidEmail: 'Please enter a valid email address',
  invalidUrl: 'Please enter a valid URL',

  // Loading States
  loading: 'Loading...',
  pleaseWait: 'Please wait...',

  // Search
  search: 'Search',
  searchPlaceholder: 'Search conversations...',
  noResults: 'No results found',

  // Time/Date
  now: 'Now',
  today: 'Today',
  yesterday: 'Yesterday',
  thisWeek: 'This week',
  lastWeek: 'Last week',

  // Permissions
  permissionDenied: 'Permission denied',
  accessRestricted: 'Access restricted',

  // Connection
  connectionError: 'Connection error',
  networkError: 'Network error',
  retryConnection: 'Retry connection',

  // Features
  comingSoon: 'Coming soon',
  betaFeature: 'Beta feature',
  experimental: 'Experimental',

  // Reasoning
  thoughtProcess: 'Thought Process',

  // Model Settings
  setApiKeyTooltip: 'Set API Key',
  modelName: 'Mysta',
} as const;
