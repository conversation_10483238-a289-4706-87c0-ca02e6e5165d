import OpenAI from 'openai';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  Agent,
  AgentConfig,
  ConditionTask,
  Tool<PERSON>all,
  ToolCallResult,
} from '@the-agent/shared';
import { GlobalContext, TaskWithState } from '~/types/task';
import { TaskContextBuilder } from './context';
import { taskTools } from '~/tools/task-toolkit';
import { initTaskState, run } from './runner';
import { DEFAULT_MAX_TOOL_CALLS } from '~/configs/common';
import { TaskToolExecutor } from '~/tools/task-executor';
import { ToolDescription } from '~/types';

export class ConditionToolExecutor extends TaskToolExecutor {
  constructor(c: GlobalContext) {
    super(c);
  }

  async executeInternal(
    task: TaskWithState,
    toolName: string,
    params: any
  ): Promise<ToolCallResult> {
    if (toolName == 'ConditionToolkit_select') {
      const p = params as { taskId: string | undefined };
      return this.select(task, p.taskId);
    } else {
      return {
        success: false,
        error: 'Invalid tool call',
      };
    }
  }

  getPostToolcallMessageInternal(toolCall: ToolCall): string {
    if (toolCall.function.name === 'ConditionToolkit_select') {
      if (toolCall.result?.success) {
        return CONDITION_SELECT_SUCCESS_MESSAGE;
      } else {
        return CONDITION_SELECT_ERROR_MESSAGE;
      }
    } else {
      throw new Error('Invalid tool call');
    }
  }

  getToolDescriptions(): ToolDescription[] {
    return [
      {
        name: 'ConditionToolkit_select',
        description: 'Select the task to run based on the given input',
        parameters: {
          type: 'object',
          properties: {
            taskId: {
              type: 'string',
              description: 'The task id to run.',
            },
          },
          required: ['taskId'],
        },
        returns: {
          type: 'object',
          description: 'The result of selected task execution',
          properties: {
            status: { type: 'string', description: 'The status of the selected task execution' },
            output: { type: 'string', description: 'The output of the selected task execution' },
          },
        },
      },
      ...taskTools,
    ];
  }

  private async select(
    task: TaskWithState,
    subtaskId: string | undefined
  ): Promise<ToolCallResult> {
    if (!subtaskId) {
      return {
        success: false,
        error: 'Invalid input: subtaskId is required',
      };
    }
    const taskList = (task.task as ConditionTask).condition.taskList;
    const selectedTask = taskList.find(t => t.task.id === subtaskId);
    if (!selectedTask) {
      return {
        success: false,
        error: 'Invalid input: subtask not exists',
      };
    }
    const subtask = selectedTask.task;
    const state = initTaskState(this.c, subtask, task);
    const result = await run(state, this.c);
    return {
      success: true,
      data: result,
    };
  }
}

export function createConditionAgent(model: string, openai: OpenAI, c: GlobalContext): Agent {
  const config: AgentConfig = {
    id: 'condition',
    llmClient: openai,
    model: model,
    systemPrompt: CONDITION_EVALUATOR_SYSTEM_PROMPT,
    contextBuilder: new TaskContextBuilder(c),
    toolExecutor: new ConditionToolExecutor(c),
    maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
  };
  return new ChatAgent(config);
}

export const CONDITION_SELECT_SUCCESS_MESSAGE = `
You have successfully selected and executed the task via ConditionToolkit_select.

Now store the result with the TaskToolkit_finishTask tool.
`;

export const CONDITION_SELECT_ERROR_MESSAGE = `
You have failed to select the task to run via ConditionToolkit_select.

You can either retry the task by running the ConditionToolkit_select tool again, or complete the task via the TaskToolkit_finishTask tool.
`;

export const CONDITION_EVALUATOR_SYSTEM_PROMPT = `
You are an task executor to process task defined as following json:

\`\`\`json
Task {
    id: string              // Unique identifier (e.g. "open-tab", "click-button")
    goal: string            // Description of what this task does
    inputsFrom?: string[]   // Optional. IDs of tasks this task depends on
    output?: string         // content and format of the expected result
    condition: {
      taskList: {
        when: string // in which condition to execute the task, should be descriptive
        task: Task   // Task to execute
      }[] // List of tasks with its condition to execute
    }
  }
\`\`\`

## Guidelines
- You will be given the inputs collected from dependent tasks(via inputsFrom) and use it to decide which task to run.
- call ConditionToolkit_select to select the task to run.
`;
