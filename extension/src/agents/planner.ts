import OpenAI from 'openai';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Agent,
  AgentConfig,
  Tool<PERSON>allResult,
  ToolCall,
  Task,
  ToolOptions,
} from '@the-agent/shared';
import { GlobalContext, RuntimeInput, TaskWithState } from '~/types/task';
import { taskTools } from '~/tools/task-toolkit';
import { initTaskState, run } from './runner';
import { TaskContextBuilder } from './context';
import { DEFAULT_MAX_DEPTH, DEFAULT_MAX_TOOL_CALLS } from '~/configs/common';
import { TaskToolExecutor } from '~/tools/task-executor';
import { ToolDescription } from '~/types';

class PlannerAgentToolExecutor extends TaskToolExecutor {
  constructor(c: GlobalContext) {
    super(c);
  }

  async executeInternal(
    task: TaskWithState,
    toolName: string,
    params: any,
    options?: ToolOptions
  ): Promise<ToolCallResult> {
    switch (toolName) {
      case 'PlannerAgent_run':
        const p1 = params as { taskList: Task[] };
        return await this.runTaskList(task, p1.taskList, options);
      default:
        return {
          success: false,
          error: 'Invalid tool call',
        };
    }
  }

  getToolDescriptions(): ToolDescription[] {
    return [
      {
        name: 'PlannerAgent_run',
        description:
          'Run a list of atomic tasks derived from a complex task. Use only if the task involves multiple independent or sequential actions that cannot be performed directly.',
        parameters: {
          type: 'object',
          properties: {
            taskList: {
              type: 'array',
              description: 'The task list to run, must be more than 1 task.',
              items: {
                type: 'object',
                description:
                  'The task instance definition. foreach, loop and condition are with nested task definitions',
                properties: {
                  id: {
                    type: 'string',
                    description: 'The id of the task, must be unique in the task list',
                  },
                  goal: {
                    type: 'string',
                    description:
                      'The goal of the task, should be descriptive and comprehensive, use bullet points if necessary',
                  },
                  inputsFrom: {
                    type: 'array',
                    description: 'a list of task ids that this task depends on',
                    items: {
                      type: 'string',
                      description: 'The id of the task that this task depends on',
                    },
                  },
                  output: {
                    type: 'string',
                    description:
                      'This field describes the content and format of the expected result',
                  },
                  canBeAtomic: {
                    type: 'boolean',
                    description: 'Whether the task can be executed directly by the browser agent',
                  },
                  foreach: {
                    type: 'object',
                    description: 'repeatitively run a task with data collected from inputsFrom',
                    properties: {
                      count: {
                        type: 'number',
                        description:
                          'The count of iterations, if not provided, the task will run with data collected from inputsFrom',
                      },
                      task: {
                        type: 'object',
                        description: 'The inline task to run in each iteration',
                      },
                    },
                  },
                },
              },
            },
          },
          required: ['taskList'],
        },
        returns: {
          type: 'object',
          description: 'The task results collected from the task list execution',
          properties: {
            results: {
              type: 'array',
              description: 'List of task results from tasks with its goal, status and output',
              items: {
                type: 'object',
                properties: {
                  goal: { type: 'string', description: 'The goal of the task' },
                  status: { type: 'string', description: 'The status of the task' },
                  output: { type: 'string', description: 'The output of the task' },
                },
              },
            },
          },
        },
      },
      ...taskTools,
    ];
  }

  getPostToolcallMessageInternal(toolCall: ToolCall): string {
    if (toolCall.function.name === 'PlannerAgent_run') {
      if (toolCall.result?.success) {
        return PLANNER_AGENT_RUN_SUCCESS_MESSAGE;
      } else {
        return PLANNER_AGENT_RUN_ERROR_MESSAGE;
      }
    } else {
      throw new Error('Invalid tool call');
    }
  }

  private async runTaskList(
    task: TaskWithState,
    taskList: Task[] | undefined,
    options?: ToolOptions
  ): Promise<ToolCallResult> {
    if (!taskList) {
      return {
        success: false,
        error: 'Invalid input: taskList is required',
      };
    }
    const results: RuntimeInput[] = [];
    const subtasks = await Promise.all(
      taskList.map(subtask => initTaskState(this.c, subtask, task))
    );
    for (const subtask of subtasks) {
      if (this.c.aborted) {
        return {
          success: false,
          error: 'Task execution aborted by user',
        };
      }

      const result = await run(subtask, this.c);
      results.push({ id: subtask.task.id, ...result });
      await options?.onToolMessageUpdate?.(results);
    }
    return {
      success: true,
      data: results,
    };
  }
}

export function createPlannerAgent(model: string, openai: OpenAI, c: GlobalContext): Agent {
  const config: AgentConfig = {
    id: 'simple',
    llmClient: openai,
    model: model,
    systemPrompt: PLANNER_SYSTEM_PROMPT,
    contextBuilder: new TaskContextBuilder(c),
    toolExecutor: new PlannerAgentToolExecutor(c),
    maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
    maxDepth: DEFAULT_MAX_DEPTH,
  };
  return new ChatAgent(config);
}

const PLANNER_AGENT_RUN_SUCCESS_MESSAGE = `You have successfully executed the task via PlannerAgent_run tool and collected the results.
Now summarize the output and store the summarized result with the TaskToolkit_finishTask tool.`;

const PLANNER_AGENT_RUN_ERROR_MESSAGE = `You have failed to execute the task via PlannerAgent_run tool.
You can either retry the task or complete the task via the TaskToolkit_finishTask tool.`;

const PLANNER_SYSTEM_PROMPT = `
You are a **Planner Agent**.
Your job is to **break down a given complex task into a list of subtasks** and **call \`PlannerAgent_run\` with these tasks.**
You **do NOT execute tasks yourself.**

---

## 🧱 Task Definition

Each task may include minimal control structures:

* \`foreach\`: run a subtask multiple times over an input list
* Inline \`Task\` objects are used for \`foreach.task\`

Each task must have:

* A **unique, short, and readable \`id\`**
* A **clear \`goal\`** describing what the subtask does
* A **structured \`output\`** (e.g., "a list of URLs", "boolean true/false")
* Optional \`inputsFrom\` or \`foreach\` if needed

Use \`inputsFrom\` to reference outputs of other tasks.

---

## 🔹 What You Must Do

1. **Break down the task into a flat list of subtasks.**

   * Avoid creating vague, high-level tasks that still require further breakdown.
   * Try to make each task **as atomic as possible**, but it does **not have to strictly be ≤ 5–10 actions**.

2. **If multiple items follow the same logic, use \`foreach\` instead of unrolling tasks.**

3. **Ensure every task is meaningful and directly contributes to completing the overall goal.**

4. **When done, call \`PlannerAgent_run\` with the full task list.**

---

## ✅ Good Example

### Original Task

*"Search for the top 5 news on CNN and summarize each headline."*

### Output

\`\`\`
PlannerAgent_run({
  tasks: [
    {
      id: "search_cnn",
      goal: "Open CNN and collect the URLs of the top 5 news articles.",
      output: "a list of 5 article URLs"
    },
    {
      id: "summarize_each_article",
      foreach: {
        inputFrom: "search_cnn",
        task: {
          id: "summarize_article",
          goal: "Open the article and summarize its headline.",
          output: "a short text summary"
        }
      }
    }
  ]
})
\`\`\`

---

## 🚫 Bad Examples

### ❌ Nested Decomposition

\`\`\`
tasks: [
  { id: "collect_news", goal: "Find CNN articles", output: "list of links" },
  { id: "process_news", goal: "Go through each article and summarize it", output: "summaries" }
]
\`\`\`

→ Too high-level; the second task still needs further breakdown.

---

### ❌ Repetitive Unrolling

\`\`\`
tasks: [
  { id: "get_first_article", goal: "Summarize article 1", output: "summary" },
  { id: "get_second_article", goal: "Summarize article 2", output: "summary" }
]
\`\`\`

→ Should use \`foreach\` instead.

---

## 🚫 Common Mistakes to Avoid

* ❌ Producing tasks that still need another planner pass.
* ❌ Manually repeating similar subtasks instead of using \`foreach\`.
* ❌ Creating overly vague high-level tasks like "process results" or "analyze data".

---

This version makes it clear:
✅ Tasks **should be as flat as possible**.
✅ Use **\`foreach\` for repetitive work**.
✅ Avoid vague high-level tasks that would require further breakdown.
`;
