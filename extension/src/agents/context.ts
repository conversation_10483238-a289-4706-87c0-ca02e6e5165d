import { ContextBuilder, MemoryOptions, Message } from '@the-agent/shared';
import { buildTaskContextPrompt } from '~/chat/prompt';
import { ContextChunk } from '~/types';
import { GlobalContext, TaskWithState } from '~/types/task';
import { buildContext, filterRecentMessages } from '~/utils/context';

const SIMPLE_MEMORY_OPTIONS: MemoryOptions = {
  recent: 10,
  related: 0,
  site: 0,
  graph: 0,
  tab: false,
};

export class TaskContextBuilder implements ContextBuilder {
  private c: GlobalContext;

  constructor(c: GlobalContext) {
    this.c = c;
  }

  async build(message: Message, options?: Partial<MemoryOptions>): Promise<Message[]> {
    const memoryOptions = { ...SIMPLE_MEMORY_OPTIONS, ...(options ?? {}) };
    const task = await getTask(this.c, message);
    const chunks: ContextChunk[] = buildTaskContextPrompt(task, this.c);
    message.content = buildContext(message, chunks);
    const recentMessages = task.state.history.slice(-memoryOptions.recent);
    return [...filterRecentMessages(recentMessages), message];
  }
}

async function getTask(c: GlobalContext, message: Message): Promise<TaskWithState> {
  if (!message.task_id) {
    throw new Error('Task ID is required');
  }
  const task = c.tasks[message.task_id];
  if (!task) {
    throw new Error('Task not found');
  }
  return task;
}
