import OpenAI from 'openai';

import { <PERSON><PERSON><PERSON><PERSON>, Agent, AgentConfig, ToolCallR<PERSON>ult, ToolCall } from '@the-agent/shared';
import { GlobalContext, TaskWithState } from '~/types/task';
import { taskTools } from '~/tools/task-toolkit';
import { runTask } from './runner';
import { TaskContextBuilder } from './context';
import { DEFAULT_MAX_DEPTH, DEFAULT_MAX_TOOL_CALLS } from '~/configs/common';
import { TaskToolExecutor } from '~/tools/task-executor';
import { ToolDescription } from '~/types';

class DispatcherAgentToolExecutor extends TaskToolExecutor {
  private browser: Agent;
  private planner: Agent;

  constructor(c: GlobalContext, browser: Agent, planner: Agent) {
    super(c);
    this.browser = browser;
    this.planner = planner;
  }

  async executeInternal(task: TaskWithState, toolName: string): Promise<ToolCallResult> {
    switch (toolName) {
      case 'BrowserAgent_run':
        return await this.browserRun(task);
      case 'PlannerAgent_run':
        return await this.plannerRun(task);
      default:
        return {
          success: false,
          error: 'Invalid tool call',
        };
    }
  }

  getToolDescriptions(): ToolDescription[] {
    return [
      {
        name: 'BrowserAgent_run',
        description: 'Delegate the task to browser agent to run',
        parameters: {
          type: 'object',
          properties: {},
          required: [],
        },
        returns: {
          type: 'object',
          description: 'The task result retured from BrowserAgent',
          properties: {
            status: {
              type: 'string',
              description: 'The status of the task',
            },
            output: {
              type: 'string',
              description: 'The output of the task',
            },
          },
        },
      },
      {
        name: 'PlannerAgent_run',
        description: 'delegate the task to planner agent to run',
        parameters: {
          type: 'object',
          properties: {},
          required: [],
        },
        returns: {
          type: 'result',
          description: 'The task results collected from subtask execution',
          properties: {
            status: {
              type: 'string',
              description: 'The status of the task',
            },
            output: {
              type: 'string',
              description: 'The output of the task',
            },
          },
        },
      },
      ...taskTools,
    ];
  }

  getPostToolcallMessageInternal(toolCall: ToolCall): string {
    if (toolCall.function.name === 'BrowserAgent_run') {
      if (toolCall.result?.success) {
        return BROWSER_AGENT_RUN_SUCCESS_MESSAGE;
      } else {
        return BROWSER_AGENT_RUN_ERROR_MESSAGE;
      }
    } else if (toolCall.function.name === 'PlannerAgent_run') {
      if (toolCall.result?.success) {
        return PLANNER_AGENT_RUN_SUCCESS_MESSAGE;
      } else {
        return PLANNER_AGENT_RUN_ERROR_MESSAGE;
      }
    } else {
      throw new Error('Invalid tool call');
    }
  }

  isFinalToolCall(toolCall: ToolCall) {
    const result = toolCall.result as ToolCallResult;
    return result.success || super.isFinalToolCall(toolCall);
  }

  private async browserRun(task: TaskWithState): Promise<ToolCallResult> {
    const result = await runTask(task, this.browser, this.c);
    return {
      success: result.status === 'completed',
      data: 'Task finished',
    };
  }

  private async plannerRun(task: TaskWithState): Promise<ToolCallResult> {
    const result = await runTask(task, this.planner, this.c);
    return {
      success: result.status === 'completed',
      data: 'Task finished',
    };
  }
}

export function createDispatcherAgent(
  model: string,
  openai: OpenAI,
  browser: Agent,
  planner: Agent,
  c: GlobalContext
): Agent {
  const config: AgentConfig = {
    id: 'simple',
    llmClient: openai,
    model: model,
    systemPrompt: DISPATCHER_SYSTEM_PROMPT,
    contextBuilder: new TaskContextBuilder(c),
    toolExecutor: new DispatcherAgentToolExecutor(c, browser, planner),
    maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
    maxDepth: DEFAULT_MAX_DEPTH,
  };
  return new ChatAgent(config);
}

const BROWSER_AGENT_RUN_SUCCESS_MESSAGE = `You have successfully executed the task. Now simply reply "Task completed" and exit. Thank you!`;

const BROWSER_AGENT_RUN_ERROR_MESSAGE = `You have failed to execute the task. You can either retry the task or complete the task via the TaskToolkit_finishTask tool.`;

const PLANNER_AGENT_RUN_SUCCESS_MESSAGE = `You have successfully brokendown and executed the task and collected the results.
Now summarize the output and store the summarized result with the TaskToolkit_finishTask tool.`;

const PLANNER_AGENT_RUN_ERROR_MESSAGE = `You have failed to breakdown and run the task. You can either retry the task or complete the task via the TaskToolkit_finishTask tool.`;

const DISPATCHER_SYSTEM_PROMPT = `
You are a **Dispatcher Agent**.
Your job is to **decide whether to execute a task directly with \`BrowserAgent_run\`** or **breakdown it further with \`PlannerAgent_run\`**.

---

## ✅ Decision Rules

### **Call \`BrowserAgent_run\`tool when:**

* The task can be done in **≤ 5–10 browser actions**
* It **does NOT include loops, conditions, or foreach logic**
* It **does NOT require complex multi-tab workflows**

🔹 **Examples – Correctly use BrowserAgent_run:**

1. "Open Twitter, search 'OpenAI', and like the first post."
2. "Go to Gmail, log in, and read the latest email subject."
3. "Open nytimes.com, find the top headline, and copy it."
4. "Search Google for 'best pizza near me' and click the first link."

---

### **Call \`PlannerAgent_run\` tool when:**

* The task **requires multiple steps that depend on each other**
* The task **involves loops (\`foreach\`), conditions, or dynamic iteration**
* The task **requires processing a list of items or complex cross-tab actions**

🔹 **Examples – Correctly use PlannerAgent_run:**

1. "Search Amazon for 'wireless headphones' and copy prices for the top 10 results."
2. "Find the top 5 YouTube videos on AI and summarize each description."
3. "Open 3 different news sites, extract the top headlines, and compare them."

---

## ⚠️ Key Principle

> **If the task can be done directly in 1 tab with ≤ 5–10 actions and no looping/conditions → Always use \`BrowserAgent_run\`.**
>
> **Only use \`PlannerAgent_run\` if the task clearly requires decomposition.**

---

## 🚫 Common Mistakes to Avoid

❌ Sending simple tasks to \`PlannerAgent_run\` (inefficient).
❌ Breaking down tasks unnecessarily.
❌ Choosing \`PlannerAgent_run\` just because the task *looks* long—decide based on actions, not words.

---

### ✅ Decision Checklist (Think Before Choosing)

1. Does the task require loops or conditions?
2. Does it require dynamic iteration or cross-tab interactions?
3. Can it be done in a single tab with ≤ 10 browser actions?

✔ If **YES to #3 and NO to #1 and #2 → Use BrowserAgent_run**
✔ Otherwise → Use PlannerAgent_run
`;
