import { Message } from '@the-agent/shared';
import { ContextChunk } from '~/types';

export function buildContext(message: Message, chunks: ContextChunk[]): string {
  const content = message.content ? `\n\n${message.content}` : '';
  return chunks.map(buildChunk).join('\n\n') + content;
}

export function filterRecentMessages(messages: Message[]): Message[] {
  const result: Message[] = [];
  for (const message of messages) {
    if (message.role === 'system') {
      continue;
    }
    if (message.role === 'user' && message.status === 'error') {
      continue;
    }
    if (message.role === 'tool') {
      result.push({
        ...message,
        content: message.status === 'completed' ? 'Tool call success' : 'Tool call failed',
        tool_calls: undefined,
      });
    } else {
      result.push(message);
    }
  }
  return result;
}

function buildChunk(chunk: ContextChunk): string {
  const title = chunk.title ? `${chunk.title}\n` : '';
  const footer = chunk.footer ? `\n${chunk.footer}` : '';
  return `${title}${chunk.content}${footer}`;
}
