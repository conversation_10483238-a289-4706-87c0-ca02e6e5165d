import { ToolCall } from '@the-agent/shared';
import OpenAI from 'openai';
import { ToolDescription, WebInteractionResult } from '~/types/tools';

// Define DOMElementNode interface
export interface DOMElementNode {
  element: Element;
  tagName: string;
  attributes: Record<string, string>;
  textContent?: string;
  id?: string;
  selector: string;
  xpath: string;
  rect?: DOMRect;
  isVisible?: boolean;
  isInViewport?: boolean;
}

export interface GetPageTextResult {
  content: string;
  url: string;
  title: string;
}

export interface BuildElementMapResult {
  totalElements: number;
  interactiveElementsString: string;
  elementMapData: [string, DOMElementNode][];
}

export interface SendKeysParams {
  keys: string;
}

export interface SendKeysResult {
  sent: boolean;
  keys: string;
}

// Key sequence types
export interface KeySequence {
  isCombo: boolean;
  keys: string[];
}

export interface KeyInfo {
  key: string;
  code: string;
  keyCode: number;
  text?: string;
}

// Command sender type for methods that need to send debugger commands
export type CommandSender = (method: string, params?: object) => Promise<object>;

/**
 * Parse key sequence string into individual key sequences
 */
export function parseKeySequence(keys: string): KeySequence[] {
  const sequences: KeySequence[] = [];

  // Split by spaces to handle multiple key sequences
  const keyParts = keys.split(' ');

  for (const part of keyParts) {
    if (part.includes('+')) {
      // This is a key combination
      sequences.push({
        isCombo: true,
        keys: part.split('+').map(k => k.trim()),
      });
    } else {
      // This is a single key
      sequences.push({
        isCombo: false,
        keys: [part.trim()],
      });
    }
  }

  return sequences;
}

/**
 * Check if a key is a modifier key
 */
export function isModifierKey(key: string): boolean {
  const modifiers = ['Control', 'Ctrl', 'Shift', 'Alt', 'Meta', 'Command', 'Cmd'];
  return modifiers.includes(key);
}

/**
 * Get modifier flags for key combinations
 */
export function getModifierFlags(modifierKeys: string[]): number {
  let flags = 0;

  for (const key of modifierKeys) {
    switch (key) {
      case 'Alt':
        flags |= 1; // Alt
        break;
      case 'Control':
      case 'Ctrl':
        flags |= 2; // Ctrl
        break;
      case 'Meta':
      case 'Command':
      case 'Cmd':
        flags |= 4; // Meta/Cmd
        break;
      case 'Shift':
        flags |= 8; // Shift
        break;
    }
  }

  return flags;
}

/**
 * Get key information including key code and other properties
 */
export function getKeyInfo(key: string): KeyInfo {
  const keyMap: Record<string, KeyInfo> = {
    // Special keys
    Escape: { key: 'Escape', code: 'Escape', keyCode: 27 },
    Enter: { key: 'Enter', code: 'Enter', keyCode: 13, text: '\r' },
    Tab: { key: 'Tab', code: 'Tab', keyCode: 9, text: '\t' },
    Backspace: { key: 'Backspace', code: 'Backspace', keyCode: 8 },
    Delete: { key: 'Delete', code: 'Delete', keyCode: 46 },
    Insert: { key: 'Insert', code: 'Insert', keyCode: 45 },
    Home: { key: 'Home', code: 'Home', keyCode: 36 },
    End: { key: 'End', code: 'End', keyCode: 35 },
    PageUp: { key: 'PageUp', code: 'PageUp', keyCode: 33 },
    PageDown: { key: 'PageDown', code: 'PageDown', keyCode: 34 },
    ArrowLeft: { key: 'ArrowLeft', code: 'ArrowLeft', keyCode: 37 },
    ArrowUp: { key: 'ArrowUp', code: 'ArrowUp', keyCode: 38 },
    ArrowRight: { key: 'ArrowRight', code: 'ArrowRight', keyCode: 39 },
    ArrowDown: { key: 'ArrowDown', code: 'ArrowDown', keyCode: 40 },
    Space: { key: ' ', code: 'Space', keyCode: 32, text: ' ' },

    // Modifier keys
    Control: { key: 'Control', code: 'ControlLeft', keyCode: 17 },
    Ctrl: { key: 'Control', code: 'ControlLeft', keyCode: 17 },
    Shift: { key: 'Shift', code: 'ShiftLeft', keyCode: 16 },
    Alt: { key: 'Alt', code: 'AltLeft', keyCode: 18 },
    Meta: { key: 'Meta', code: 'MetaLeft', keyCode: 91 },
    Command: { key: 'Meta', code: 'MetaLeft', keyCode: 91 },
    Cmd: { key: 'Meta', code: 'MetaLeft', keyCode: 91 },

    // Function keys
    F1: { key: 'F1', code: 'F1', keyCode: 112 },
    F2: { key: 'F2', code: 'F2', keyCode: 113 },
    F3: { key: 'F3', code: 'F3', keyCode: 114 },
    F4: { key: 'F4', code: 'F4', keyCode: 115 },
    F5: { key: 'F5', code: 'F5', keyCode: 116 },
    F6: { key: 'F6', code: 'F6', keyCode: 117 },
    F7: { key: 'F7', code: 'F7', keyCode: 118 },
    F8: { key: 'F8', code: 'F8', keyCode: 119 },
    F9: { key: 'F9', code: 'F9', keyCode: 120 },
    F10: { key: 'F10', code: 'F10', keyCode: 121 },
    F11: { key: 'F11', code: 'F11', keyCode: 122 },
    F12: { key: 'F12', code: 'F12', keyCode: 123 },
  };

  // Check if it's a known special key
  if (keyMap[key]) {
    return keyMap[key];
  }

  // For regular characters, use the character itself
  if (key.length === 1) {
    const char = key.toLowerCase();
    const keyCode = char.charCodeAt(0);
    return {
      key: key,
      code: `Key${char.toUpperCase()}`,
      keyCode: keyCode >= 97 && keyCode <= 122 ? keyCode - 32 : keyCode,
      text: key,
    };
  }

  // Fallback
  return { key: key, code: key, keyCode: 0, text: key };
}

/**
 * Send a key combination (e.g., Ctrl+C, Ctrl+Shift+T)
 */
export async function sendKeyCombo(keys: string[], sendCommand: CommandSender): Promise<void> {
  const modifiers: Array<{ key: string; code: string; keyCode: number }> = [];
  let mainKey: KeyInfo | null = null;

  // Separate modifiers from the main key
  for (const key of keys) {
    const keyInfo = getKeyInfo(key);
    if (isModifierKey(key)) {
      modifiers.push(keyInfo);
    } else {
      mainKey = keyInfo;
    }
  }

  try {
    // Press modifiers down
    for (const modifier of modifiers) {
      await sendCommand('Input.dispatchKeyEvent', {
        type: 'keyDown',
        key: modifier.key,
        code: modifier.code,
        windowsVirtualKeyCode: modifier.keyCode,
        nativeVirtualKeyCode: modifier.keyCode,
        modifiers: getModifierFlags(modifiers.map(m => m.key)),
      });
    }

    // Press main key down and up
    if (mainKey) {
      await sendCommand('Input.dispatchKeyEvent', {
        type: 'keyDown',
        key: mainKey.key,
        code: mainKey.code,
        windowsVirtualKeyCode: mainKey.keyCode,
        nativeVirtualKeyCode: mainKey.keyCode,
        modifiers: getModifierFlags(modifiers.map(m => m.key)),
        text: mainKey.text,
      });

      await sendCommand('Input.dispatchKeyEvent', {
        type: 'keyUp',
        key: mainKey.key,
        code: mainKey.code,
        windowsVirtualKeyCode: mainKey.keyCode,
        nativeVirtualKeyCode: mainKey.keyCode,
        modifiers: getModifierFlags(modifiers.map(m => m.key)),
      });
    }

    // Release modifiers
    for (let i = modifiers.length - 1; i >= 0; i--) {
      const modifier = modifiers[i];
      await sendCommand('Input.dispatchKeyEvent', {
        type: 'keyUp',
        key: modifier.key,
        code: modifier.code,
        windowsVirtualKeyCode: modifier.keyCode,
        nativeVirtualKeyCode: modifier.keyCode,
        modifiers: getModifierFlags(modifiers.slice(0, i).map(m => m.key)),
      });
    }
  } catch (error) {
    console.error('Error sending key combo:', error);
    throw error;
  }
}

/**
 * Send a single key press
 */
export async function sendSingleKey(key: string, sendCommand: CommandSender): Promise<void> {
  const keyInfo = getKeyInfo(key);

  await sendCommand('Input.dispatchKeyEvent', {
    type: 'keyDown',
    key: keyInfo.key,
    code: keyInfo.code,
    windowsVirtualKeyCode: keyInfo.keyCode,
    nativeVirtualKeyCode: keyInfo.keyCode,
    text: keyInfo.text,
  });

  await sendCommand('Input.dispatchKeyEvent', {
    type: 'keyUp',
    key: keyInfo.key,
    code: keyInfo.code,
    windowsVirtualKeyCode: keyInfo.keyCode,
    nativeVirtualKeyCode: keyInfo.keyCode,
  });
}

/**
 * Returns the markdown conversion function to be executed in the tab context
 */
export function getMarkdownConversionFunction(): () => WebInteractionResult<GetPageTextResult> {
  return () => {
    // TurndownService implementation - simplified version without external dependencies
    interface TurndownRule {
      filter: string | string[] | ((node: HTMLElement) => boolean);
      replacement: (content: string, node: HTMLElement) => string;
    }

    class SimpleTurndownService {
      private rules: TurndownRule[] = [];

      constructor() {
        this.initializeDefaultRules();
      }

      private initializeDefaultRules() {
        // Remove script, style, and other non-content elements
        this.addRule({
          filter: ['script', 'style', 'noscript', 'meta', 'link', 'head'],
          replacement: () => '',
        });

        // Convert line breaks
        this.addRule({
          filter: 'br',
          replacement: () => '\n',
        });

        // Convert headings
        this.addRule({
          filter: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
          replacement: (content, node) => {
            const level = parseInt(node.tagName.charAt(1));
            return '\n' + '#'.repeat(level) + ' ' + content.trim() + '\n\n';
          },
        });

        // Convert paragraphs
        this.addRule({
          filter: 'p',
          replacement: content => '\n' + content.trim() + '\n\n',
        });

        // Convert links
        this.addRule({
          filter: 'a',
          replacement: (content, node) => {
            const href = (node as HTMLAnchorElement).href;
            return href && href !== '#' ? `[${content.trim()}](${href})` : content;
          },
        });

        // Convert strong/bold
        this.addRule({
          filter: ['strong', 'b'],
          replacement: content => `**${content.trim()}**`,
        });

        // Convert emphasis/italic
        this.addRule({
          filter: ['em', 'i'],
          replacement: content => `_${content.trim()}_`,
        });

        // Convert code
        this.addRule({
          filter: 'code',
          replacement: content => `\`${content.trim()}\``,
        });

        // Convert lists
        this.addRule({
          filter: 'ul',
          replacement: content => '\n' + content + '\n',
        });

        this.addRule({
          filter: 'ol',
          replacement: content => '\n' + content + '\n',
        });

        this.addRule({
          filter: 'li',
          replacement: (content, node) => {
            const parent = node.parentElement;
            if (parent?.tagName === 'OL') {
              const index = Array.from(parent.children).indexOf(node) + 1;
              return `${index}. ${content.trim()}\n`;
            } else {
              return `- ${content.trim()}\n`;
            }
          },
        });

        // Convert blockquotes
        this.addRule({
          filter: 'blockquote',
          replacement: content => {
            return (
              content
                .split('\n')
                .map(line => (line.trim() ? `> ${line.trim()}` : '>'))
                .join('\n') + '\n\n'
            );
          },
        });

        // Convert horizontal rules
        this.addRule({
          filter: 'hr',
          replacement: () => '\n---\n\n',
        });

        // Clean up navigation and UI elements
        this.addRule({
          filter: (node: HTMLElement) => {
            const className = node.className || '';
            const id = node.id || '';
            return (
              className.includes('nav') ||
              className.includes('menu') ||
              className.includes('sidebar') ||
              className.includes('header') ||
              className.includes('footer') ||
              id.includes('nav') ||
              id.includes('menu') ||
              id.includes('header') ||
              id.includes('footer')
            );
          },
          replacement: () => '',
        });
      }

      addRule(rule: TurndownRule) {
        this.rules.push(rule);
      }

      turndown(htmlString: string): string {
        try {
          const tempDiv = document.createElement('div');
          tempDiv.innerHTML = htmlString;
          return this.processNode(tempDiv);
        } catch (error) {
          throw new Error(
            `Turndown conversion failed: ${error instanceof Error ? error.message : 'Unknown error'}`
          );
        }
      }

      private processNode(node: Node): string {
        if (node.nodeType === Node.TEXT_NODE) {
          return node.textContent || '';
        }

        if (node.nodeType !== Node.ELEMENT_NODE) {
          return '';
        }

        const element = node as HTMLElement;
        const tagName = element.tagName.toLowerCase();

        // Apply rules
        for (const rule of this.rules) {
          let matches = false;

          if (typeof rule.filter === 'function') {
            matches = rule.filter(element);
          } else if (Array.isArray(rule.filter)) {
            matches = rule.filter.includes(tagName);
          } else {
            matches = rule.filter === tagName;
          }

          if (matches) {
            const content = Array.from(element.childNodes)
              .map(child => this.processNode(child))
              .join('');
            return rule.replacement(content, element);
          }
        }

        // Default processing for unmatched elements
        const content = Array.from(element.childNodes)
          .map(child => this.processNode(child))
          .join('');

        // For block elements, add spacing
        const blockElements = [
          'div',
          'section',
          'article',
          'aside',
          'main',
          'header',
          'footer',
          'nav',
        ];
        if (blockElements.includes(tagName)) {
          return content ? '\n' + content.trim() + '\n' : '';
        }

        return content;
      }
    }

    try {
      const turndownService = new SimpleTurndownService();

      let html: string;
      try {
        html = document.documentElement.outerHTML;
      } catch {
        html = document.body?.outerHTML || '';
      }

      const markdown = turndownService.turndown(html);

      return {
        success: true,
        data: {
          content: markdown.trim(),
          url: window.location.href,
          title: document.title,
        },
      };
    } catch (error) {
      // Fallback to plain text extraction
      try {
        const text =
          document.body?.innerText ||
          document.documentElement.innerText ||
          'Error extracting page text';

        return {
          success: true,
          data: {
            content: text,
            url: window.location.href,
            title: document.title,
          },
        };
      } catch {
        return {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown conversion error',
        };
      }
    }
  };
}

/**
 * Returns the buildElementMap function that generates both string output and element map
 *
 * This function is designed to work around Chrome extension's executeScript limitations
 * where closure variables cannot be properly serialized. Instead of relying on closure,
 * we pass parameters through executeScript's args parameter.
 *
 */
export function buildElementMapFunction(): (
  ...args: string[]
) => WebInteractionResult<BuildElementMapResult> {
  return (...args: string[]) => {
    const fullXPath = args[0] && args[0] !== '' ? args[0] : undefined;

    // Define all necessary types and interfaces within the function scope
    interface DOMElementNode {
      element: Element;
      tagName: string;
      attributes: Record<string, string>;
      textContent?: string;
      id?: string;
      selector: string;
      xpath: string;
      rect?: DOMRect;
      isVisible?: boolean;
      isInViewport?: boolean;
    }

    interface HighlightedElement extends Omit<DOMElementNode, 'element'> {
      element?: Element;
      highlight_index: string;
      is_new?: boolean;
      type: string;
      interactionType: 'click' | 'input' | 'select' | 'submit' | 'navigate';
      value?: string;
      placeholder?: string;
      ariaLabel?: string;
      role?: string;
      disabled?: boolean;
      hidden?: boolean;
      required?: boolean;
      checked?: boolean;
      selected?: boolean;
      formId?: string;
      formName?: string;
      labelText?: string;
      associatedLabels?: string[];
    }

    // Global map for highlight_index collision detection
    const globalHighlightIndexMap = new Map<string, string>();

    /**
     * FNV-1a hash algorithm implementation - better distribution than simple hash
     * FNV (Fowler-Noll-Vo) is a non-cryptographic hash function with good distribution properties
     */
    function fnv1aHash(str: string): string {
      let hash = 0x811c9dc5; // FNV offset basis for 32-bit

      for (let i = 0; i < str.length; i++) {
        hash ^= str.charCodeAt(i);
        hash = Math.imul(hash, 0x01000193); // FNV prime for 32-bit
      }

      return (hash >>> 0).toString(16); // Convert to unsigned 32-bit hex
    }

    function generateHighlightIndex(xpath: string): string {
      if (!xpath || xpath.trim() === '') {
        throw new Error('XPath cannot be empty for highlight index generation');
      }

      const hash = fnv1aHash(xpath);
      let n = 4;

      // Try progressively longer substrings to avoid collisions
      while (n <= Math.min(hash.length, 16)) {
        // Cap at 16 chars for readability
        const candidate = hash.slice(-n);
        const existingXpath = globalHighlightIndexMap.get(candidate);

        if (!existingXpath) {
          globalHighlightIndexMap.set(candidate, xpath);
          return candidate;
        } else if (existingXpath === xpath) {
          return candidate;
        } else {
          n++;
        }
      }

      // Fallback: use full hash + timestamp suffix for uniqueness
      const uniqueHash = hash + Date.now().toString(16).slice(-4);
      globalHighlightIndexMap.set(uniqueHash, xpath);
      return uniqueHash;
    }

    // Cache for computed styles
    const computedStyleCache = new WeakMap<Element, CSSStyleDeclaration>();

    function getCachedComputedStyle(element: Element): CSSStyleDeclaration | null {
      if (computedStyleCache.has(element)) {
        return computedStyleCache.get(element)!;
      }

      try {
        const style = window.getComputedStyle(element);
        computedStyleCache.set(element, style);
        return style;
      } catch {
        return null;
      }
    }

    function isInteractiveElement(element: Element): boolean {
      if (!element || element.nodeType !== Node.ELEMENT_NODE) {
        return false;
      }

      const tagName = element.tagName.toLowerCase();
      const style = getCachedComputedStyle(element);

      // Check for inherently interactive HTML elements
      const interactiveElements = new Set([
        'a',
        'button',
        'input',
        'select',
        'textarea',
        'details',
        'summary',
        'option',
        'dialog',
        'audio',
        'video',
        'form',
      ]);

      if (interactiveElements.has(tagName)) {
        if ('disabled' in element && (element as HTMLInputElement).disabled) {
          return false;
        }
        if ('readOnly' in element && (element as HTMLInputElement).readOnly) {
          return false;
        }
        return true;
      }

      // Check for interactive ARIA roles
      const role = element.getAttribute('role');
      const interactiveRoles = new Set([
        'button',
        'link',
        'checkbox',
        'menuitem',
        'menuitemcheckbox',
        'menuitemradio',
        'option',
        'radio',
        'slider',
        'switch',
        'tab',
        'textbox',
        'treeitem',
        'combobox',
        'searchbox',
      ]);

      if (role && interactiveRoles.has(role.toLowerCase())) {
        return true;
      }

      // Check for interactive cursor style
      if (style && style.cursor === 'pointer') {
        return true;
      }

      // Check for common event handlers
      if (
        element.hasAttribute('onclick') ||
        element.hasAttribute('onmousedown') ||
        element.hasAttribute('ontouchstart') ||
        element.hasAttribute('onkeydown') ||
        element.hasAttribute('onchange') ||
        element.hasAttribute('oninput')
      ) {
        return true;
      }

      // Check for contenteditable
      if (
        element.getAttribute('contenteditable') === 'true' ||
        (element as HTMLElement).isContentEditable
      ) {
        return true;
      }

      // Check for tabindex
      const tabindex = element.getAttribute('tabindex');
      if (tabindex !== null && tabindex !== '-1') {
        return true;
      }

      // Check for interactive class patterns
      const className = element.className || '';
      const interactiveClassPatterns = [
        /\bbutton\b/i,
        /\bclick/i,
        /\blink\b/i,
        /\bicon\b/i,
        /\bmenu/i,
        /\btab\b/i,
        /\bclose\b/i,
        /\bopen\b/i,
        /\btoggle\b/i,
        /\bselect/i,
        /\bsearch/i,
        /\bsubmit\b/i,
        /\bplay\b/i,
        /\bpause\b/i,
        /\bnext\b/i,
        /\bprev/i,
        /\bcard\b/i,
        /\bitem\b/i,
      ];

      if (
        typeof className === 'string' &&
        interactiveClassPatterns.some(pattern => pattern.test(className))
      ) {
        if (style) {
          if (
            style.cursor === 'pointer' ||
            style.userSelect === 'none' ||
            parseFloat(style.opacity) > 0.5
          ) {
            return true;
          }
        }

        const textContent = element.textContent?.trim();
        if (textContent || element.children.length > 0) {
          return true;
        }
      }

      // Check for data attributes that suggest interactivity
      const dataAttributes = [
        'data-action',
        'data-click',
        'data-toggle',
        'data-target',
        'data-href',
        'data-url',
        'data-link',
        'data-tab',
        'data-menu',
        'data-dismiss',
        'data-close',
        'data-open',
        'data-submit',
        'data-search',
      ];

      if (dataAttributes.some(attr => element.hasAttribute(attr))) {
        return true;
      }

      // Check for specific patterns in id or data-testid
      const id = element.id || '';
      const testId = element.getAttribute('data-testid') || '';
      const idPatterns = [
        /\bbutton\b/i,
        /\bclick/i,
        /\blink\b/i,
        /\bmenu/i,
        /\bsearch/i,
        /\bsubmit\b/i,
        /\bclose\b/i,
        /\bopen\b/i,
        /\btoggle\b/i,
      ];

      if (
        (id && idPatterns.some(pattern => pattern.test(id))) ||
        (testId && idPatterns.some(pattern => pattern.test(testId)))
      ) {
        return true;
      }

      // Check for styled interactive elements
      if (style) {
        const hasBoxShadow = style.boxShadow && style.boxShadow !== 'none';
        const hasBorder = style.border && style.border !== 'none' && style.borderWidth !== '0px';
        const hasBackground =
          style.backgroundColor &&
          style.backgroundColor !== 'rgba(0, 0, 0, 0)' &&
          style.backgroundColor !== 'transparent';
        const hasTransition = style.transition && style.transition !== 'none';

        if ((hasBoxShadow || hasBorder || hasBackground) && hasTransition) {
          const textContent = element.textContent?.trim();
          if (textContent && textContent.length < 100) {
            return true;
          }
        }
      }

      return false;
    }

    function findInteractiveElements(scopeElement?: Element): Element[] {
      const foundElements = new Set<Element>();
      const searchRoot = scopeElement || document;

      // Traditional selector-based approach
      const interactiveSelectors = [
        'button',
        'input',
        'select',
        'textarea',
        'a[href]',
        'summary',
        'details',
        '[role="button"]',
        '[role="link"]',
        '[role="checkbox"]',
        '[role="radio"]',
        '[role="menuitem"]',
        '[role="tab"]',
        '[role="switch"]',
        '[role="combobox"]',
        '[role="slider"]',
        '[role="searchbox"]',
        '[role="textbox"]',
        '[role="option"]',
        '[tabindex]:not([tabindex="-1"])',
        '[contenteditable="true"]',
        '[onclick]',
        '[onmousedown]',
        '[onkeydown]',
        '[onchange]',
        '[oninput]',
        '[style*="cursor: pointer"]',
        '[style*="cursor:pointer"]',
      ];

      const selectorElements = searchRoot.querySelectorAll(interactiveSelectors.join(','));
      selectorElements.forEach(element => foundElements.add(element));

      // Enhanced detection for potential interactive elements
      const potentialInteractiveSelectors = [
        '[class*="button"]',
        '[class*="btn"]',
        '[class*="click"]',
        '[class*="link"]',
        '[class*="icon"]',
        '[class*="menu"]',
        '[class*="tab"]',
        '[class*="close"]',
        '[class*="open"]',
        '[class*="toggle"]',
        '[class*="select"]',
        '[class*="search"]',
        '[class*="submit"]',
        '[class*="card"]',
        '[class*="item"]',
        '[id*="button"]',
        '[id*="btn"]',
        '[id*="click"]',
        '[id*="link"]',
        '[id*="menu"]',
        '[id*="search"]',
        '[id*="submit"]',
        '[data-action]',
        '[data-click]',
        '[data-toggle]',
        '[data-target]',
        '[data-href]',
        '[data-url]',
      ];

      const potentialElements = searchRoot.querySelectorAll(
        potentialInteractiveSelectors.join(',')
      );
      potentialElements.forEach(element => {
        if (isInteractiveElement(element)) {
          foundElements.add(element);
        }
      });

      // Additional scan for elements with pointer cursor
      const allElements = searchRoot.querySelectorAll('*');
      allElements.forEach(element => {
        if (foundElements.has(element)) return;

        const style = getCachedComputedStyle(element);
        if (style && style.cursor === 'pointer' && isInteractiveElement(element)) {
          foundElements.add(element);
        }
      });

      return Array.from(foundElements);
    }

    // Utility functions
    function isElementHidden(element: Element): boolean {
      const style = element.getAttribute('style');
      const computedStyle = getCachedComputedStyle(element);

      return (
        element.hasAttribute('hidden') ||
        (element.hasAttribute('aria-hidden') && element.getAttribute('aria-hidden') === 'true') ||
        (style != null &&
          (style.includes('display: none') ||
            style.includes('display:none') ||
            style.includes('visibility: hidden') ||
            style.includes('visibility:hidden'))) ||
        (computedStyle != null &&
          (computedStyle.display === 'none' ||
            computedStyle.visibility === 'hidden' ||
            computedStyle.opacity === '0'))
      );
    }

    function getElementAttributes(element: Element): Record<string, string> {
      const attributes: Record<string, string> = {};
      for (let i = 0; i < element.attributes.length; i++) {
        const attr = element.attributes[i];
        attributes[attr.name] = attr.value;
      }
      return attributes;
    }

    function getElementValue(element: Element): string | undefined {
      if ('value' in element) {
        return (element as HTMLInputElement).value || undefined;
      }
      return element.getAttribute('value') || undefined;
    }

    function isElementChecked(element: Element): boolean {
      if ('checked' in element) {
        return (element as HTMLInputElement).checked;
      }
      return element.getAttribute('aria-checked') === 'true';
    }

    function getAssociatedLabels(element: Element): string[] {
      const labels: string[] = [];

      if (['input', 'select', 'textarea'].includes(element.tagName.toLowerCase())) {
        if (element.id) {
          const labelElements = document.querySelectorAll(`label[for="${element.id}"]`);
          labelElements.forEach(label => {
            if (label.textContent) {
              labels.push(label.textContent.trim());
            }
          });
        }

        let parent = element.parentElement;
        while (parent && parent !== document.body) {
          if (parent.tagName === 'LABEL' && parent.textContent) {
            labels.push(parent.textContent.trim());
            break;
          }
          parent = parent.parentElement;
        }
      }

      return labels;
    }

    function getFormContext(element: Element): { formId?: string; formName?: string } {
      let formId: string | undefined;
      let formName: string | undefined;

      if ('form' in element) {
        const form = (element as HTMLInputElement).form;
        if (form) {
          formId = form.id || undefined;
          formName = form.getAttribute('name') || undefined;
        }
      } else {
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
          if (parent.tagName === 'FORM') {
            formId = parent.id || undefined;
            formName = parent.getAttribute('name') || undefined;
            break;
          }
          parent = parent.parentElement;
        }
      }

      return { formId, formName };
    }

    function getCssSelector(element: Element): string {
      if (element.id) {
        return `#${element.id}`;
      }

      let selector = element.tagName.toLowerCase();

      if (element.classList.length > 0) {
        selector += `.${Array.from(element.classList).join('.')}`;
      }

      if (element.parentElement) {
        const siblings = Array.from(element.parentElement.children);
        const sameTagSiblings = siblings.filter(el => el.tagName === element.tagName);
        if (sameTagSiblings.length > 1) {
          const index = sameTagSiblings.indexOf(element as Element) + 1;
          selector += `:nth-of-type(${index})`;
        }
      }

      return selector;
    }

    function getFullXPath(element: Element): string {
      if (element === document.documentElement) {
        return '/html';
      }

      if (!element.parentElement) {
        return '';
      }

      const tagName = element.tagName.toLowerCase();
      const siblings = Array.from(element.parentElement.children);
      const sameTagSiblings = siblings.filter(el => el.tagName.toLowerCase() === tagName);

      const parentPath = getFullXPath(element.parentElement);

      if (sameTagSiblings.length === 1) {
        return `${parentPath}/${tagName}`;
      } else {
        const index = sameTagSiblings.indexOf(element) + 1;
        return `${parentPath}/${tagName}[${index}]`;
      }
    }

    function groupElementsBySemanticSegment(
      elements: HighlightedElement[]
    ): Record<string, HighlightedElement[]> {
      const grouped: Record<string, HighlightedElement[]> = {
        header: [],
        nav: [],
        main: [],
        section: [],
        article: [],
        aside: [],
        footer: [],
        other: [],
      };

      const semanticSelectors = ['header', 'nav', 'main', 'section', 'article', 'aside', 'footer'];
      const semanticElements = new Map<Element, string>();

      semanticSelectors.forEach(tagName => {
        const elements = document.querySelectorAll(tagName);
        elements.forEach(element => {
          semanticElements.set(element, tagName);
        });
      });

      elements.forEach(element => {
        let assigned = false;

        for (const [semanticElement, segmentType] of semanticElements.entries()) {
          if (element.element && semanticElement.contains(element.element)) {
            grouped[segmentType].push(element);
            assigned = true;
            break;
          }
        }

        if (!assigned) {
          grouped.other.push(element);
        }
      });

      return grouped;
    }

    function formatElement(element: HighlightedElement, depth: number, is_new?: boolean): string {
      let displayText = '';
      let attributeText = '';

      // Get display text
      if (element.ariaLabel) {
        displayText = element.ariaLabel;
      } else if (element.labelText) {
        displayText = element.labelText;
      } else if (element.placeholder) {
        displayText = element.placeholder;
      } else if (element.textContent) {
        displayText = element.textContent.replace(/\s+/g, ' ').trim();
      } else if (element.value) {
        displayText = element.value;
      } else if (element.attributes.title) {
        displayText = element.attributes.title;
      } else if (element.attributes.alt) {
        displayText = element.attributes.alt;
      }

      // If displayText is still empty, infer from attributes
      if (!displayText.trim()) {
        let inferredText = '';
        const classAttr = element.attributes.class;
        const idAttr = element.id;

        if (classAttr) {
          const meaningfulClasses = classAttr
            .split(' ')
            .filter(c => !c.startsWith('data-v-') && isNaN(parseInt(c)) && c.length > 2);

          if (meaningfulClasses.length > 0) {
            inferredText = meaningfulClasses.join(' ').replace(/[-_]/g, ' ').trim();
          }
        }

        if (!inferredText && idAttr) {
          inferredText = idAttr.replace(/[-_]/g, ' ').trim();
        }
        displayText = inferredText;
      }

      if (displayText.length > 50) {
        displayText = displayText.substring(0, 47) + '...';
      }

      const importantAttrs: string[] = [];
      if (element.ariaLabel) {
        importantAttrs.push(`aria-label="${element.ariaLabel}"`);
      }
      if (element.placeholder && element.tagName === 'input') {
        importantAttrs.push(`placeholder="${element.placeholder}"`);
      }
      if (element.attributes.type && element.tagName === 'input') {
        importantAttrs.push(`type="${element.attributes.type}"`);
      }
      if (element.disabled) {
        importantAttrs.push('disabled');
      }
      if (element.required) {
        importantAttrs.push('required');
      }
      if (element.checked) {
        importantAttrs.push('checked');
      }
      if (element.attributes.href) {
        const href =
          element.attributes.href.length > 30
            ? element.attributes.href.substring(0, 27) + '...'
            : element.attributes.href;
        importantAttrs.push(`href="${href}"`);
      }

      if (importantAttrs.length > 0) {
        attributeText = ' ' + importantAttrs.join(' ');
      }

      const indentation = '\t'.repeat(depth);
      const newMarker = is_new ? '*' : '';

      return `${indentation}${newMarker}[${element.highlight_index}]${newMarker}<${element.tagName}${attributeText}>${displayText}`;
    }

    function interactiveElementsToString(elements: HighlightedElement[]): string {
      const elementMap = new Map(elements.map(e => [e.element, e]));
      const childrenMap = new Map<HighlightedElement, HighlightedElement[]>();
      const roots: HighlightedElement[] = [];

      for (const element of elements) {
        if (!element.element) continue;

        let parentEl = element.element.parentElement;
        let parentFound = false;
        while (parentEl) {
          if (elementMap.has(parentEl)) {
            const parent = elementMap.get(parentEl)!;
            if (!childrenMap.has(parent)) {
              childrenMap.set(parent, []);
            }
            childrenMap.get(parent)!.push(element);
            parentFound = true;
            break;
          }
          parentEl = parentEl.parentElement;
        }
        if (!parentFound) {
          roots.push(element);
        }
      }

      // Sort by DOM position
      const sortByPosition = (a: HighlightedElement, b: HighlightedElement): number => {
        if (!a.element || !b.element) return 0;
        const pos = a.element.compareDocumentPosition(b.element);
        if (pos & Node.DOCUMENT_POSITION_FOLLOWING) {
          return -1;
        }
        if (pos & Node.DOCUMENT_POSITION_PRECEDING) {
          return 1;
        }
        return 0;
      };

      for (const children of childrenMap.values()) {
        children.sort(sortByPosition).reverse();
      }

      const result: string[] = [];
      const processNode = (node: HighlightedElement, depth: number) => {
        result.push(formatElement(node, depth, node.is_new));
        const children = childrenMap.get(node);
        if (children) {
          for (const child of children) {
            processNode(child, depth + 1);
          }
        }
      };

      // Group root elements by semantic segment
      const rootsBySegment = groupElementsBySemanticSegment(roots);

      Object.entries(rootsBySegment).forEach(([segmentName, segmentElements]) => {
        if (segmentElements.length > 0) {
          if (segmentName !== 'other') {
            result.push(`{${segmentName}}`);
          }

          segmentElements.sort(sortByPosition).reverse();
          segmentElements.forEach(root => {
            processNode(root, 0);
          });

          if (segmentName !== 'other') {
            result.push('');
          }
        }
      });

      // Remove trailing empty line
      while (result.length > 0 && result[result.length - 1] === '') {
        result.pop();
      }

      return result.join('\n');
    }

    function analyzePageDOM(fullXPath?: string): {
      totalElements: number;
      interactiveElementsString: string;
      elementMapData: [string, DOMElementNode][];
    } {
      if (!document || document.readyState === 'loading') {
        throw new Error('Document not ready for analysis');
      }

      const highlightedElements: HighlightedElement[] = [];
      const elementMap = new Map<string, DOMElementNode>();

      try {
        // Find scope element if fullXPath is provided
        let scopeElement: Element | undefined;
        if (fullXPath) {
          try {
            const result = document.evaluate(
              fullXPath,
              document,
              null,
              XPathResult.FIRST_ORDERED_NODE_TYPE,
              null
            );
            scopeElement = result.singleNodeValue as Element;
            if (!scopeElement) {
              throw new Error(`Element with XPath "${fullXPath}" not found`);
            }
          } catch (error) {
            throw new Error(`Invalid XPath or element not found: ${fullXPath} ${error}`);
          }
        }

        const elements = findInteractiveElements(scopeElement);

        elements.forEach(element => {
          if (isElementHidden(element)) {
            return;
          }

          if (highlightedElements.some(he => he.element === element)) {
            return;
          }

          const fullElementXPath = getFullXPath(element);
          const highlight_index = generateHighlightIndex(fullElementXPath);
          const tagName = element.tagName.toLowerCase();
          const attributes = getElementAttributes(element);

          let interactionType: HighlightedElement['interactionType'] = 'click';
          if (tagName === 'input' || tagName === 'textarea') {
            interactionType = 'input';
          } else if (tagName === 'select') {
            interactionType = 'select';
          } else if (tagName === 'form' || attributes.type === 'submit') {
            interactionType = 'submit';
          } else if (tagName === 'a' && attributes.href) {
            interactionType = 'navigate';
          }

          let type = tagName;
          if (attributes.type) {
            type = `${tagName}[type="${attributes.type}"]`;
          } else if (attributes.role) {
            type = `${tagName}[role="${attributes.role}"]`;
          }

          const associatedLabels = getAssociatedLabels(element);
          const { formId, formName } = getFormContext(element);

          const highlightedElement: HighlightedElement = {
            element: element,
            highlight_index,
            tagName,
            type,
            interactionType,
            attributes,
            textContent: element.textContent?.trim() || undefined,
            id: element.id || undefined,
            selector: getCssSelector(element),
            xpath: fullElementXPath,
            value: getElementValue(element),
            placeholder: attributes.placeholder,
            ariaLabel: attributes['aria-label'],
            role: attributes.role,
            disabled: element.hasAttribute('disabled') || attributes['aria-disabled'] === 'true',
            hidden: isElementHidden(element),
            required: element.hasAttribute('required') || attributes['aria-required'] === 'true',
            checked: isElementChecked(element),
            selected: element.hasAttribute('selected') || attributes['aria-selected'] === 'true',
            formId,
            formName,
            labelText: associatedLabels.length > 0 ? associatedLabels[0] : undefined,
            associatedLabels: associatedLabels.length > 0 ? associatedLabels : undefined,
          };

          highlightedElements.push(highlightedElement);

          // Create DOMElementNode for element map
          const elementNode: DOMElementNode = {
            element,
            tagName,
            attributes,
            textContent: element.textContent?.trim() || undefined,
            id: element.id || undefined,
            selector: getCssSelector(element),
            xpath: fullElementXPath,
            isVisible: !isElementHidden(element),
          };

          elementMap.set(highlight_index, elementNode);
        });

        const interactiveElementsString = interactiveElementsToString(highlightedElements);

        return {
          totalElements: highlightedElements.length,
          interactiveElementsString,
          elementMapData: Array.from(elementMap.entries()),
        };
      } catch (error) {
        console.error('Error during DOM analysis:', error);
        throw new Error(
          `DOM analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`
        );
      }
    }

    // Execute the analysis and return result
    try {
      const result = analyzePageDOM(fullXPath);
      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error during analysis',
      };
    }
  };
}

export function toOpenAITools(toolDescription: ToolDescription[]): OpenAI.ChatCompletionTool[] {
  return toolDescription.map(tool => ({
    type: 'function' as const,
    function: {
      name: tool.name,
      description: tool.description,
      parameters: tool.parameters,
    },
  }));
}

export function parseToolParams(toolCall: ToolCall): unknown {
  try {
    return toolCall.function.arguments ? JSON.parse(toolCall.function.arguments) : {};
  } catch (error) {
    console.error('Error parsing tool arguments:', error);
    return {};
  }
}
