import { Storage } from '@plasmohq/storage';
import { TabToolkit } from '../tools/tab-toolkit';
import { InputElementParams, SendKeysParams, WebToolkit } from '../tools/web-toolkit';
import { RuntimeAction, RuntimeActionResponse } from '../types/messages';
import { WebInteractionResult } from '~/types/tools';

import { db } from '~/storages/indexdb';

const storage = new Storage();
const webToolkit = new WebToolkit();

async function openSidePanel(tab: chrome.tabs.Tab) {
  if (!tab?.id) {
    console.error('Invalid tab id');
    return;
  }

  try {
    await chrome.sidePanel.setOptions({
      tabId: tab.id,
      enabled: true,
      path: 'sidepanel.html',
    });
    // Do NOT call chrome.sidePanel.open here!
  } catch (error) {
    console.error('Failed to enable side panel:', error);
  }
}

if (typeof chrome !== 'undefined' && chrome.sidePanel && chrome.sidePanel.setPanelBehavior) {
  // Ensure clicking the extension icon opens the side panel
  chrome.sidePanel.setPanelBehavior({ openPanelOnActionClick: true }).catch(console.error);
}

if (typeof chrome !== 'undefined' && chrome.action) {
  chrome.action.onClicked.addListener(async tab => {
    await openSidePanel(tab);
  });
} else {
  console.error('chrome.action API is not available');
}

type TabToolKitArguments = { url: string } | { tabId: number };

type WebToolKitArguments =
  | Record<string, never>
  | {
      selector: string;
    }
  | { selectors: string }
  | { hashKey: string }
  | InputElementParams
  | SendKeysParams;

type GetDialogsArguments = {
  limit?: number;
  offset?: number;
  chatTitle?: string;
  isPublic?: boolean;
  isFree?: boolean;
  status?: string;
  sortBy?: string;
  sortOrder?: string;
};

type GetMessagesArguments = {
  chatId: string;
  limit?: number;
  offset?: number;
  messageText?: string;
  senderId?: string;
  startTimestamp?: number;
  endTimestamp?: number;
  sortBy?: string;
  sortOrder?: string;
};

type SearchMessagesArguments = {
  query: string;
  chatId?: string;
  topK?: number;
  messageRange?: number;
  threshold?: number;
  isPublic?: boolean;
  isFree?: boolean;
};

type TgToolKitArguments = GetDialogsArguments | GetMessagesArguments | SearchMessagesArguments;

chrome.runtime.onMessage.addListener(
  (
    message: RuntimeAction,
    _sender,
    sendResponse: (response: WebInteractionResult<unknown> | RuntimeActionResponse) => void
  ) => {
    // Update frontend to support action
    const action = message;
    if (action.name === 'ping') {
      sendResponse({ success: true, message: 'ping' });
    }

    if (action.name === 'inject-script') {
      (async () => {
        const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
        if (!tabs || tabs.length !== 1) {
          sendResponse({ success: false, error: 'no active tab found' });
        }
        const currentTab = tabs[0];
        const { code } = action.body as { code: string };
        const blob = new Blob([code], { type: 'text/javascript' });
        const url = URL.createObjectURL(blob);
        chrome.scripting.executeScript({
          target: { tabId: currentTab.id! },
          files: [url],
        });
      })();
    }

    if (action.name === 'load-scripts') {
      (async () => {
        try {
          const installations = await db.getAllInstallations();
          const scripts = installations.map(i => i.code);

          sendResponse({ success: true, data: scripts });
        } catch (err) {
          console.error('Error loading scripts:', err);
          sendResponse({ success: false, error: String(err) });
        }
      })();
      return true; // keep sendResponse async
    }

    if (action.name === 'execute-tool') {
      const { name, arguments: params } =
        (action.body as {
          name: string;
          arguments: TabToolKitArguments | WebToolKitArguments | TgToolKitArguments;
        }) || {};
      if (!name || !params) {
        sendResponse({ success: false, error: 'Invalid tool execution request' });
        return true;
      }

      (async () => {
        try {
          if (!chrome?.tabs) {
            throw new Error('chrome.tabs API is not available');
          }
          if (name.startsWith('WebToolkit_')) {
            const toolName = name.replace('WebToolkit_', '');
            const args = params as WebToolKitArguments;
            let result;
            switch (toolName) {
              case 'getPageText':
                result = await webToolkit.getPageText();
                break;
              case 'screenshot':
                result = await webToolkit.screenshot();
                break;
              case 'refreshPage':
                result = await webToolkit.refreshPage();
                break;
              case 'buildElementMap':
                result = await webToolkit.buildElementMap(
                  (args as { fullXPath?: string }).fullXPath
                );
                break;
              case 'clickElementByKey':
                result = await webToolkit.clickElementByKey((args as { hashKey: string }).hashKey);
                break;
              case 'inputElementByKey':
                result = await webToolkit.inputElementByKey(args as InputElementParams);
                break;
              case 'scrollToElementByKey':
                result = await webToolkit.scrollToElementByKey(
                  (args as { hashKey: string }).hashKey
                );
                break;
              case 'sendKeys':
                result = await webToolkit.sendKeys(args as SendKeysParams);
                break;
              default:
                throw new Error(`Unknown WebToolkit operation: ${toolName}`);
            }

            sendResponse(result as WebInteractionResult<unknown>);
            return true;
          }

          if (name.startsWith('TabToolkit_')) {
            const toolNoolName = name.replace('TabToolkit_', '');
            const args = params as TabToolKitArguments;
            switch (toolNoolName) {
              case 'openTab':
                const result = await TabToolkit.openTab((args as { url: string }).url);
                if (result.success && result.data?.tabId) {
                  webToolkit.setTargetTab(result.data.tabId);
                }
                sendResponse(result);
                return true;
              case 'listTabs':
                const listResult = await TabToolkit.listTabs();
                sendResponse(listResult);
                return true;
              case 'closeTab':
                const closeResult = await TabToolkit.closeTab((args as { tabId: number }).tabId);
                sendResponse(closeResult);
                return true;
              case 'switchToTab':
                const tabId = (args as { tabId: number }).tabId;
                const switchResult = await TabToolkit.switchToTab(tabId);
                if (switchResult.success) {
                  webToolkit.setTargetTab(tabId);
                }
                sendResponse(switchResult);
                return true;
              case 'waitForTabLoad':
                const waitForResult = await TabToolkit.waitForTabLoad(
                  (args as { tabId: number }).tabId
                );
                sendResponse(waitForResult);
                return true;
              case 'getCurrentActiveTab':
                const getCurrentActiveTabResult = await TabToolkit.getCurrentActiveTab();
                sendResponse(getCurrentActiveTabResult);
                return true;
              default:
                sendResponse({
                  success: false,
                  error: `Tool ${name} not implemented in background script`,
                });
            }
            return true;
          }
        } catch (error: unknown) {
          console.error('Error executing tool in background:', error);
          const message = error instanceof Error ? error.message : JSON.stringify(error);
          sendResponse({ success: false, error: message });
        }
      })();

      return true;
    }

    if (message.name === 'update-config') {
      (async () => {
        try {
          const { key, value } = message.body as { key: string; value: string };
          await storage.set(key, value);
          sendResponse({ success: true });
        } catch (error: unknown) {
          console.error('Error updating config:', error);
          const message = error instanceof Error ? error.message : JSON.stringify(error);
          sendResponse({
            success: false,
            error: message,
          });
        }
      })();

      return true;
    }
  }
);
