import { MemoryItem, MemoryRelation } from '@the-agent/shared';
import { ContextChunk } from '~/types';
import { GlobalContext, TaskWithState } from '~/types/task';

export function buildMemoryFactsContextChunk(memories: MemoryItem[]): ContextChunk | undefined {
  if (!memories || memories.length === 0) {
    return undefined;
  }

  const memoryContext = memories.map(mem => mem.memory).join('\n\n');

  return {
    title: 'User Conversation History',
    content: `## Previous Conversation Context\n\n${memoryContext}`,
  };
}

export function buildSiteMemoryContextPrompt(
  relatedSiteMessages: MemoryItem[] = []
): ContextChunk | undefined {
  if (relatedSiteMessages.length === 0) {
    return undefined;
  }
  const siteKnowledge = relatedSiteMessages.map(msg => msg.memory).join('\n\n');
  return {
    title: 'Website Operation Knowledge',
    content: `## Known Website Operations\n\n${siteKnowledge}`,
  };
}

export function buildProceduralMemoryContextChunk(
  memories: MemoryItem[]
): ContextChunk | undefined {
  if (!memories || memories.length === 0) {
    return undefined;
  }

  // filter out procedural memories
  const proceduralMemories = memories.filter(mem => mem.metadata?.memoryType === 'procedural');

  if (proceduralMemories.length === 0) {
    return undefined;
  }

  const proceduralContext = proceduralMemories.map(mem => mem.memory).join('\n\n');

  return {
    title: 'Task Execution History',
    content: `## Previous Task Execution Steps\n\n${proceduralContext}`,
  };
}

export function buildWebTabContextPrompt(
  activeTab: chrome.tabs.Tab | undefined,
  availableTabs: chrome.tabs.Tab[]
): ContextChunk | undefined {
  if (!activeTab) {
    return undefined;
  }

  const tabContext = `Active Tab: ${activeTab.title} (${activeTab.url})
Available Tabs: ${availableTabs.map(tab => `${tab.title} (${tab.url})`).join(', ')}`;

  return {
    title: 'Current Browser State',
    content: `## Current Browser Information\n\n${tabContext}`,
  };
}

export function buildGraphContextPrompt(
  graphRelations: MemoryRelation[] = []
): ContextChunk | undefined {
  if (graphRelations.length === 0) {
    return undefined;
  }
  let contextPrompt = '';
  for (const rel of graphRelations) {
    const src = rel.source;
    const dst = rel.destination;
    const relType = rel.relationship;
    contextPrompt += `- ${src} --[${relType}]--> ${dst}\n`;
  }
  return {
    title: 'Knowledge Graph Relations',
    content: `## Related Knowledge Connections\n\n${contextPrompt}`,
  };
}

export function buildTaskContextPrompt(task: TaskWithState, c: GlobalContext): ContextChunk[] {
  const chunks = [
    {
      title: '## Current Task:',
      content: JSON.stringify(task.task),
    },
    {
      title: '## Control Context:',
      content: `
- Execution Path: ${c.processing.join(' → ')}
- Depth: ${task.state.depth}
      `,
    },
  ];
  const runtimeInputs = task.state.runtimeInputProcessed ?? task.state.runtimeInputs;
  if (runtimeInputs && runtimeInputs.length > 0) {
    return [
      {
        title: '## Input Values: ',
        content: JSON.stringify(runtimeInputs),
      },
      ...chunks,
    ];
  } else {
    return chunks;
  }
}
