import { WebInteractionResult } from '~/types/tools';
import {
  getMarkdownConversionFunction,
  buildElementMapFunction,
  parseKeySequence,
  sendKey<PERSON>ombo,
  sendSingleKey,
} from '~/utils/toolkit';

// Define DOMElementNode interface here since we can't import it in executeScript
interface DOMElementNode {
  element: Element;
  tagName: string;
  attributes: Record<string, string>;
  textContent?: string;
  id?: string;
  selector: string;
  xpath: string;
  rect?: DOMRect;
  isVisible?: boolean;
  isInViewport?: boolean;
}

interface DebuggerState {
  attached: boolean;
  tabId: number | null;
  targetId: string | null;
}

interface GetPageTextResult {
  content: string;
  url: string;
  title: string;
}

export interface ScreenshotResult {
  url: string;
}

interface RefreshPageResult {
  url: string;
  title: string;
  loadTime: string;
  status: string;
}

interface BuildElementMapResult {
  totalElements: number;
  interactiveElementsString: string;
  elementMap: Map<string, DOMElementNode>;
}

interface ClickElementByKeyResult {
  clicked: boolean;
  elementFound: boolean;
  elementStillExists: boolean;
  elementInfo?: {
    tagName: string;
    textContent?: string;
    attributes: Record<string, string>;
  };
}

interface InputElementByKeyResult {
  text?: string;
  value: string;
  html: string;
}

interface ScrollToElementByKeyResult {
  scrolled: boolean;
  elementFound: boolean;
  elementStillExists: boolean;
}

export interface InputElementParams {
  hashKey: string;
  value: string;
  options?: {
    clearFirst?: boolean;
    delay?: number;
    pressEnterAfterInput?: boolean;
  };
}

export interface DragElementParams {
  hashKey: string;
  targetX: number;
  targetY: number;
  options?: {
    duration?: number;
    steps?: number;
  };
}

export interface SendKeysParams {
  keys: string;
}

interface SendKeysResult {
  sent: boolean;
  keys: string;
}

export class WebToolkit {
  private debuggerState: DebuggerState = {
    attached: false,
    tabId: null,
    targetId: null,
  };

  private hasDebuggerSupport = typeof chrome.debugger?.attach === 'function';
  private targetTabId: number | null = null;

  /**
   * Set the target tab ID for agent operations
   * This ensures agent operations continue on the intended tab even if user switches tabs manually
   */
  setTargetTab(tabId: number): void {
    this.targetTabId = tabId;
  }

  /**
   * Clear the target tab ID, falling back to active tab behavior
   */
  clearTargetTab(): void {
    this.targetTabId = null;
  }

  /**
   * Normalize hashKey by removing surrounding brackets if present
   * Handles cases where AI model includes brackets in hashKey (e.g., '[30d6]' -> '30d6')
   * Only processes complete bracket pairs to avoid corrupting partial brackets
   */
  private normalizeHashKey(hashKey: string): string {
    const original = hashKey;
    // Remove complete surrounding brackets [hashKey] -> hashKey
    // Uses capturing group to extract inner content only if fully wrapped
    const normalized = hashKey.replace(/^\[(.+)\]$/, '$1');

    // Log normalization for debugging purposes
    if (original !== normalized) {
      console.debug(`[WebToolkit] Normalized hashKey: '${original}' -> '${normalized}'`);
    }

    return normalized;
  }

  private async attachDebugger(tabId: number): Promise<void> {
    if (!this.hasDebuggerSupport) {
      return;
    }

    if (this.debuggerState.attached && this.debuggerState.tabId === tabId) {
      return;
    }

    try {
      await chrome.debugger.attach({ tabId }, '1.3');
      this.debuggerState.attached = true;
      this.debuggerState.tabId = tabId;
    } catch (error) {
      console.error('Failed to attach debugger:', error);
      this.hasDebuggerSupport = false; // Disable debugger support
    }
  }

  private async detachDebugger(): Promise<void> {
    if (!this.hasDebuggerSupport || !this.debuggerState.attached || !this.debuggerState.tabId) {
      return;
    }

    try {
      await chrome.debugger.detach({ tabId: this.debuggerState.tabId });
      this.debuggerState.attached = false;
      this.debuggerState.tabId = null;
    } catch (error) {
      console.error('Failed to detach debugger:', error);
    }
  }

  private async sendCommand(method: string, params: object = {}): Promise<object> {
    if (!this.hasDebuggerSupport || !this.debuggerState.attached || !this.debuggerState.tabId) {
      throw new Error('Debugger not available');
    }

    try {
      return await chrome.debugger.sendCommand({ tabId: this.debuggerState.tabId }, method, params);
    } catch (error) {
      console.error(`Failed to execute command ${method}:`, error);
      throw error;
    }
  }

  private async getCurrentTab(): Promise<chrome.tabs.Tab> {
    try {
      // 1. If agent has set a target tab, use that regardless of which tab is currently active
      if (this.targetTabId !== null) {
        try {
          const targetTab = await chrome.tabs.get(this.targetTabId);
          if (targetTab?.id) {
            return targetTab;
          }
        } catch (error) {
          console.error(
            `Target tab ${this.targetTabId} no longer exists, falling back to active tab. error: ${error}`
          );
          this.targetTabId = null; // Clear invalid target tab
        }
      }

      // 2. Fall back to current active tab behavior
      const currentTab = await chrome.tabs.getCurrent();
      if (currentTab?.id) {
        return currentTab;
      }

      // 3. Try querying the active tab of the current window
      const [tab] = await chrome.tabs.query({
        active: true,
        lastFocusedWindow: true,
      });
      if (tab?.id) {
        return tab;
      }

      // 4. Try querying all active tabs
      const tabs = await chrome.tabs.query({
        active: true,
        windowType: 'normal',
      });
      if (tabs.length > 0) {
        return tabs[0];
      }

      throw new Error(
        'No active tab found. Please ensure the extension has the necessary permissions.'
      );
    } catch (error) {
      console.error('Error getting current tab:', error);
      throw error;
    }
  }

  private async executeInTab<T>(
    userFunc: (...args: string[]) => T,
    args: string[] = []
  ): Promise<T> {
    try {
      const tab = await this.getCurrentTab();

      if (!tab?.id) {
        console.error('No tab ID found. Tab info:', tab);
        throw new Error('Tab ID not found. Please ensure the tab is properly loaded.');
      }

      // Execute script
      const [result] = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        func: userFunc,
        args,
      });

      if (!result) {
        console.error('Script execution returned no result');
        throw new Error('No result returned from script execution');
      }

      // If result is a Promise, wait for it to complete
      if (result.result instanceof Promise) {
        const promiseResult = await result.result;
        // Check if Promise result contains error
        if (
          promiseResult &&
          typeof promiseResult === 'object' &&
          'success' in promiseResult &&
          !promiseResult.success
        ) {
          throw new Error(promiseResult.error || 'Unknown error in Promise result');
        }
        return promiseResult;
      }

      // Check if result contains error
      if (
        result.result &&
        typeof result.result === 'object' &&
        'success' in result.result &&
        !result.result.success
      ) {
        if ('error' in result.result) {
          throw new Error(result.result.error as string);
        }
        throw new Error('Unknown error in result');
      }

      return result.result as T;
    } catch (error) {
      console.error('executeInTab error:', error);
      // Check if this is a permission issue
      if (error instanceof Error && error.toString().includes('Cannot access')) {
        throw new Error(
          'Cannot access this page. Make sure the extension has permissions for this URL.'
        );
      }
      throw error; // Propagate error upward
    }
  }

  async getPageText(): Promise<WebInteractionResult<GetPageTextResult>> {
    try {
      const tab = await this.getCurrentTab();
      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      const result = await this.executeInTab<WebInteractionResult<GetPageTextResult>>(
        getMarkdownConversionFunction()
      );

      return result;
    } catch (error) {
      console.error('Error getting page text:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async screenshot(): Promise<WebInteractionResult<ScreenshotResult>> {
    try {
      // Get current tab
      const tab = await this.getCurrentTab();

      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      // Use chrome.tabs.captureVisibleTab for screenshot
      const dataUrl = await new Promise<string>((resolve, reject) => {
        chrome.tabs.captureVisibleTab(tab.windowId, { format: 'jpeg', quality: 80 }, dataUrl => {
          if (chrome.runtime.lastError) {
            reject(new Error(chrome.runtime.lastError.message));
            return;
          }
          resolve(dataUrl);
        });
      });

      return { success: true, data: { url: dataUrl } };
    } catch (error) {
      console.error('Error taking screenshot:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async refreshPage(
    url?: string,
    waitForLoad: boolean = true,
    timeout: number = 5000
  ): Promise<WebInteractionResult<RefreshPageResult>> {
    try {
      // Get current tab
      const tab = await this.getCurrentTab();
      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      // Record start time
      const startTime = Date.now();

      await this.executeInTab(
        (url?: string) => {
          return new Promise<WebInteractionResult<undefined>>(resolve => {
            if (url) {
              window.location.href = url;
            } else {
              location.reload();
            }
            resolve({ success: true, data: undefined });
          });
        },
        url ? [url] : []
      );

      // Wait for page load completion if needed
      if (waitForLoad) {
        await new Promise<void>((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            chrome.tabs.onUpdated.removeListener(listener);
            reject(new Error('Page load timeout'));
          }, timeout);

          const listener = (tabId: number, info: chrome.tabs.TabChangeInfo) => {
            if (tabId === tab.id && info.status === 'complete') {
              clearTimeout(timeoutId);
              chrome.tabs.onUpdated.removeListener(listener);
              resolve();
            }
          };

          chrome.tabs.onUpdated.addListener(listener);
        });
      }

      // Get page state
      const pageState = await this.executeInTab<{ url: string; title: string; readyState: string }>(
        () => {
          return {
            url: window.location.href,
            title: document.title,
            readyState: document.readyState,
          };
        }
      );

      // Calculate load time
      const loadTime = Date.now() - startTime;

      return {
        success: true,
        data: {
          url: pageState.url,
          title: pageState.title,
          loadTime: loadTime.toString(),
          status: pageState.readyState,
        },
      };
    } catch (error) {
      console.error('Error refreshing page:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async buildElementMap(fullXPath?: string): Promise<WebInteractionResult<BuildElementMapResult>> {
    try {
      const tab = await this.getCurrentTab();
      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      const result = await this.executeInTab<
        WebInteractionResult<{
          totalElements: number;
          interactiveElementsString: string;
          elementMapData: [string, DOMElementNode][];
        }>
      >(buildElementMapFunction(), fullXPath ? [fullXPath] : ['']);

      if (result.success && result.data) {
        return {
          success: true,
          data: {
            totalElements: result.data.totalElements,
            interactiveElementsString: result.data.interactiveElementsString,
            elementMap: new Map(result.data.elementMapData),
          },
        };
      }

      return result as WebInteractionResult<BuildElementMapResult>;
    } catch (error) {
      console.error('Error building element map:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async clickElementByKey(hashKey: string): Promise<WebInteractionResult<ClickElementByKeyResult>> {
    try {
      // Get element map by building the element map
      const analysisResult = await this.buildElementMap();

      if (!analysisResult.success || !analysisResult.data?.elementMap) {
        return {
          success: false,
          error: 'Failed to build element map.',
        };
      }

      const elementMap = analysisResult.data.elementMap;

      const result = await this.executeInTab<WebInteractionResult<ClickElementByKeyResult>>(
        (hashKey: string, elementMapData: string) => {
          const elementMap = new Map(JSON.parse(elementMapData) as [string, DOMElementNode][]);

          const elementInfo = elementMap.get(hashKey);
          if (!elementInfo) {
            return {
              success: false,
              error: `Element with hash key ${hashKey} not found.`,
              data: {
                clicked: false,
                elementFound: false,
                elementStillExists: false,
              },
            };
          }

          // Find element by selector
          const element = document.querySelector(elementInfo.selector) as HTMLElement;
          if (!element) {
            return {
              success: false,
              error: `Element with selector "${elementInfo.selector}" no longer exists on the page.`,
              data: {
                clicked: false,
                elementFound: false,
                elementStillExists: false,
              },
            };
          }

          // Check if element is visible and interactive
          const rect = element.getBoundingClientRect();
          if (rect.width === 0 || rect.height === 0) {
            return {
              success: false,
              error: 'Element is not visible or has zero dimensions.',
              data: {
                clicked: false,
                elementFound: true,
                elementStillExists: true,
                elementInfo: {
                  tagName: element.tagName.toLowerCase(),
                  textContent: element.textContent?.trim(),
                  attributes: elementInfo.attributes,
                },
              },
            };
          }

          // Scroll element into view
          element.scrollIntoView({ behavior: 'smooth', block: 'center' });

          // Perform click
          try {
            element.click();

            return {
              success: true,
              data: {
                clicked: true,
                elementFound: true,
                elementStillExists: document.querySelector(elementInfo.selector) !== null,
                elementInfo: {
                  tagName: element.tagName.toLowerCase(),
                  textContent: element.textContent?.trim(),
                  attributes: elementInfo.attributes,
                },
              },
            };
          } catch (clickError) {
            return {
              success: false,
              error: `Click failed: ${clickError instanceof Error ? clickError.message : 'Unknown click error'}`,
              data: {
                clicked: false,
                elementFound: true,
                elementStillExists: true,
                elementInfo: {
                  tagName: element.tagName.toLowerCase(),
                  textContent: element.textContent?.trim(),
                  attributes: elementInfo.attributes,
                },
              },
            };
          }
        },
        [this.normalizeHashKey(hashKey), JSON.stringify(Array.from(elementMap.entries()))]
      );

      return result;
    } catch (error) {
      console.error('Error clicking element by key:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  async inputElementByKey({
    hashKey,
    value,
    options,
  }: InputElementParams): Promise<WebInteractionResult<InputElementByKeyResult>> {
    try {
      const tab = await this.getCurrentTab();
      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      // Get element map by building the element map
      const analysisResult = await this.buildElementMap();

      if (!analysisResult.success || !analysisResult.data?.elementMap) {
        return {
          success: false,
          error: 'Failed to build element map.',
        };
      }

      const elementMap = analysisResult.data.elementMap;

      // 1. Get element info using hash key
      const elementInfo = await this.executeInTab<
        WebInteractionResult<{
          x: number;
          y: number;
          visible: boolean;
          text?: string;
          html: string;
          selector: string;
        }>
      >(
        (hashKey: string, elementMapData: string) => {
          const elementMap = new Map(JSON.parse(elementMapData) as [string, DOMElementNode][]);

          const elementData = elementMap.get(hashKey);
          if (!elementData) {
            return {
              success: false,
              error: `Element with hash key ${hashKey} not found.`,
            };
          }

          const element = document.querySelector(elementData.selector) as HTMLElement;
          if (!element) {
            return {
              success: false,
              error: `Element with selector "${elementData.selector}" no longer exists on the page.`,
            };
          }

          const rect = element.getBoundingClientRect();
          return {
            success: true,
            data: {
              x: rect.left + rect.width / 2,
              y: rect.top + rect.height / 2,
              visible: rect.width > 0 && rect.height > 0,
              text: element.textContent || undefined,
              html: element.outerHTML,
              selector: elementData.selector,
            },
          };
        },
        [this.normalizeHashKey(hashKey), JSON.stringify(Array.from(elementMap.entries()))]
      );

      if (!elementInfo || !elementInfo.success) {
        return {
          success: false,
          error: elementInfo?.error || 'Failed to get element information',
        };
      }

      if (!elementInfo.data?.visible) {
        return {
          success: false,
          error: 'Element is not visible',
        };
      }

      // 2. Attach debugger
      await this.attachDebugger(tab.id);

      // 3. Simulate mouse move to element
      await this.sendCommand('Input.dispatchMouseEvent', {
        type: 'mouseMoved',
        x: elementInfo.data.x,
        y: elementInfo.data.y,
        button: 'none',
        buttons: 0,
      });

      // 4. Simulate click to get focus
      await this.sendCommand('Input.dispatchMouseEvent', {
        type: 'mousePressed',
        x: elementInfo.data.x,
        y: elementInfo.data.y,
        button: 'left',
        buttons: 1,
        clickCount: 1,
      });

      await this.sendCommand('Input.dispatchMouseEvent', {
        type: 'mouseReleased',
        x: elementInfo.data.x,
        y: elementInfo.data.y,
        button: 'left',
        buttons: 0,
        clickCount: 1,
      });

      // 5. Clear if needed
      if (options?.clearFirst) {
        const currentValue = await this.executeInTab<string>(
          selector => (document.querySelector(selector) as HTMLInputElement)?.value || '',
          [elementInfo.data.selector]
        );

        for (let i = 0; i < currentValue.length; i++) {
          await this.sendCommand('Input.dispatchKeyEvent', {
            type: 'keyDown',
            key: 'Backspace',
            code: 'Backspace',
          });
          await this.sendCommand('Input.dispatchKeyEvent', {
            type: 'keyUp',
            key: 'Backspace',
            code: 'Backspace',
          });
          await new Promise(resolve => setTimeout(resolve, 10));
        }
      }

      // 6. Input text
      await this.sendCommand('Input.insertText', {
        text: value,
      });

      // 7. Wait for completion
      await new Promise(resolve => setTimeout(resolve, 100));

      if (options?.pressEnterAfterInput) {
        await this.sendCommand('Input.dispatchKeyEvent', {
          type: 'keyDown',
          key: 'Enter',
          code: 'Enter',
          windowsVirtualKeyCode: 13,
          nativeVirtualKeyCode: 13,
          text: '\r',
        });
        await this.sendCommand('Input.dispatchKeyEvent', {
          type: 'keyUp',
          key: 'Enter',
          code: 'Enter',
          windowsVirtualKeyCode: 13,
          nativeVirtualKeyCode: 13,
        });
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // 8. Get final result (optional verification)
      const finalResult = await this.executeInTab<WebInteractionResult<InputElementByKeyResult>>(
        (selector: string) => {
          const element = document.querySelector(selector) as HTMLInputElement;
          if (!element) {
            // Element not found after input - this is acceptable as DOM might have changed
            return {
              success: true,
              data: {
                value: '', // We can't get the actual value, but operation was successful
                text: undefined,
                html: '',
              },
            };
          }
          return {
            success: true,
            data: {
              value: element.value,
              text: element.textContent || undefined,
              html: element.outerHTML,
            },
          };
        },
        [elementInfo.data.selector]
      );

      // 10. Detach debugger
      await this.detachDebugger();

      if (!finalResult.success) {
        return {
          success: false,
          error: finalResult.error,
        };
      }

      return {
        success: true,
        data: {
          text: finalResult.data.text,
          value: finalResult.data.value,
          html: finalResult.data.html,
        },
      };
    } catch (error) {
      console.error('Error in inputElementByKey:', error);
      await this.detachDebugger();
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async scrollToElementByKey(
    hashKey: string
  ): Promise<WebInteractionResult<ScrollToElementByKeyResult>> {
    try {
      // Get element map by building the element map
      const analysisResult = await this.buildElementMap();

      if (!analysisResult.success || !analysisResult.data?.elementMap) {
        return {
          success: false,
          error: 'Failed to build element map.',
          data: {
            scrolled: false,
            elementFound: false,
            elementStillExists: false,
          },
        };
      }

      const elementMap = analysisResult.data.elementMap;

      const result = await this.executeInTab<WebInteractionResult<ScrollToElementByKeyResult>>(
        (hashKey: string, elementMapData: string) => {
          const elementMap = new Map(JSON.parse(elementMapData) as [string, DOMElementNode][]);

          const elementData = elementMap.get(hashKey);
          if (!elementData) {
            return {
              success: false,
              error: `Element with hash key ${hashKey} not found.`,
              data: {
                scrolled: false,
                elementFound: false,
                elementStillExists: false,
              },
            };
          }

          const element = document.querySelector(elementData.selector) as HTMLElement;
          if (!element) {
            return {
              success: false,
              error: `Element with selector "${elementData.selector}" no longer exists on the page.`,
              data: {
                scrolled: false,
                elementFound: true,
                elementStillExists: false,
              },
            };
          }

          try {
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            return {
              success: true,
              data: {
                scrolled: true,
                elementFound: true,
                elementStillExists: true,
              },
            };
          } catch (scrollError) {
            return {
              success: false,
              error: `Scroll failed: ${scrollError instanceof Error ? scrollError.message : 'Unknown scroll error'}`,
              data: {
                scrolled: false,
                elementFound: true,
                elementStillExists: true,
              },
            };
          }
        },
        [this.normalizeHashKey(hashKey), JSON.stringify(Array.from(elementMap.entries()))]
      );

      return result;
    } catch (error) {
      console.error('Error scrolling to element by key:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  async sendKeys({ keys }: SendKeysParams): Promise<WebInteractionResult<SendKeysResult>> {
    try {
      const tab = await this.getCurrentTab();
      if (!tab?.id) {
        throw new Error('No active tab found');
      }

      // Attach debugger
      await this.attachDebugger(tab.id);

      // Parse the keys string and send appropriate key events
      const keySequences = parseKeySequence(keys);

      for (const keySequence of keySequences) {
        if (keySequence.isCombo) {
          // Handle key combinations like Control+c, Control+Shift+T
          await sendKeyCombo(keySequence.keys, this.sendCommand.bind(this));
        } else {
          // Handle single keys
          await sendSingleKey(keySequence.keys[0], this.sendCommand.bind(this));
        }
      }

      // Detach debugger
      await this.detachDebugger();

      return {
        success: true,
        data: {
          sent: true,
          keys: keys,
        },
      };
    } catch (error) {
      console.error('Error in sendKeys:', error);
      await this.detachDebugger();
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }
}
