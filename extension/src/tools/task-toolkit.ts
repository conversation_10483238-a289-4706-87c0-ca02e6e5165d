import { Tool<PERSON>allResult } from '@the-agent/shared';
import { ToolDescription } from '~/types';
import { GlobalContext, TaskStatus, TaskWithState } from '~/types/task';

export function finishTask(
  c: GlobalContext,
  task: TaskWithState,
  result: {
    status: TaskStatus;
    output: string;
  }
): ToolCallResult {
  task.state.result = result;
  task.state.completed_at = new Date().toISOString();
  c.processing.splice(c.processing.indexOf(task.task.id), 1);
  c.processed.push(task.task.id);
  return {
    success: true,
    data: { message: 'Task completed successfully' },
  };
}

export const taskTools: ToolDescription[] = [
  {
    name: 'TaskToolkit_finishTask',
    description: 'Finish the task with task result',
    parameters: {
      type: 'object',
      properties: {
        status: {
          type: 'string',
          description: 'The status of the task execution, must be "completed" or "error".',
        },
        output: {
          type: 'string',
          description: 'The output of the task execution.',
        },
      },
      required: ['status', 'output'],
    },
    returns: {
      type: 'null',
      description: 'No return value.',
    },
  },
];

export const TASK_DONE_MESSAGE = `You have successfully executed the task and stored the result to database.
Simply reply "Task Completed" and exit. Thank you!`;
