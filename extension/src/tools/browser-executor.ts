import { Tool<PERSON><PERSON>, Too<PERSON><PERSON>xecutor, ToolCallResult, Message } from '@the-agent/shared';
import { getToolDescriptions } from './tool-descriptions';
import OpenAI from 'openai';
import { GlobalContext, TaskWithState } from '~/types/task';
import { parseToolParams, toOpenAITools } from '~/utils/toolkit';
import { taskTools } from './task-toolkit';
import { TaskToolExecutor } from './task-executor';
import { ToolDescription } from '~/types';

export class BrowserToolExecutor implements ToolExecutor {
  async execute(toolCall: ToolCall): Promise<ToolCallResult> {
    try {
      if (!toolCall.function.name) {
        throw new Error('Tool name is required');
      }

      const toolName = toolCall.function.name;
      const params = parseToolParams(toolCall);
      return await this.executeInternal(toolName, params);
    } catch (error) {
      const message = error instanceof Error ? error.message : String(error);
      console.error('Tool execution failed:', message);
      return { success: false, error: message };
    }
  }

  getTools(): OpenAI.ChatCompletionTool[] {
    return toOpenAITools(getToolDescriptions());
  }

  getPostToolcallMessage(toolCall: ToolCall): string {
    if (!toolCall.result) {
      throw new Error('Tool call result is required');
    }
    return '';
  }

  async executeInternal(toolName: string, params: any): Promise<ToolCallResult> {
    if (toolName.startsWith('TabToolkit_')) {
      return await this.executeTabToolkit(toolName, params);
    } else if (toolName.startsWith('WebToolkit_')) {
      return await this.executeWebToolkit(toolName, params);
    } else {
      throw new Error(`Unsupported tool call: ${toolName}`);
    }
  }

  private async executeWebToolkit(name: string, params: any): Promise<ToolCallResult> {
    const message = {
      name: 'execute-tool',
      body: { name, arguments: params },
    };

    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, response => {
        if (chrome.runtime.lastError) {
          console.error('WebToolkit error:', chrome.runtime.lastError);
          reject(chrome.runtime.lastError);
          return;
        }

        if (!response) {
          const error = new Error('No response received from background script');
          console.error(error);
          reject(error);
          return;
        }

        if (!response.success) {
          const error = new Error(response.error || 'Unknown error');
          console.error('WebToolkit failed:', error);
          reject(error);
          return;
        }

        resolve(response);
      });
    });
  }

  private async executeTabToolkit(name: string, params: any): Promise<ToolCallResult> {
    const message = {
      name: 'execute-tool',
      body: { name, arguments: params },
    };

    return new Promise((resolve, reject) => {
      chrome.runtime.sendMessage(message, response => {
        if (chrome.runtime.lastError) {
          reject(chrome.runtime.lastError);
          return;
        }

        if (!response) {
          const error = new Error('No response received from background script');
          console.error(error);
          reject(error);
          return;
        }

        if (!response.success) {
          const error = new Error(response.error || 'Unknown error');
          console.error('TabToolkit failed:', error);
          reject(error);
          return;
        }

        resolve(response);
      });
    });
  }
}

export class BrowserTaskToolExecutor extends TaskToolExecutor {
  private browserToolExecutor: BrowserToolExecutor;

  constructor(c: GlobalContext) {
    super(c);
    this.browserToolExecutor = new BrowserToolExecutor();
  }

  getToolDescriptions(): ToolDescription[] {
    return [...getToolDescriptions(), ...taskTools];
  }

  setReminderMessageContent(toolCall: ToolCall, _reminderMessage: Message): void {
    throw new Error(`Invalid tool call: ${toolCall.function.name}`);
  }

  getPostToolcallMessageInternal(_toolCall: ToolCall): string {
    return '';
  }

  isFinalToolCall(toolCall: ToolCall) {
    return toolCall.function.name === 'TaskToolkit_finishTask';
  }

  async executeInternal(
    _task: TaskWithState,
    toolName: string,
    params: any
  ): Promise<ToolCallResult> {
    return this.browserToolExecutor.executeInternal(toolName, params);
  }
}
