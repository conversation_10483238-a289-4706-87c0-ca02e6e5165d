import { APIClient } from '@the-agent/shared';
import { env } from '../../configs/env';
import { getApiKey } from '../../storages/cache';
import { showLoginModal } from '~/utils/global-event';
import { Api<PERSON><PERSON> } from '~/types';

/**
 * Create API client instance with authentication
 */
export const createApiClient = async (apiKey?: ApiKey | null): Promise<APIClient> => {
  if (!apiKey) {
    apiKey = await getApiKey();
  }
  if (!apiKey?.enabled) {
    showLoginModal();
    throw new Error('Authentication required');
  }
  return new APIClient({
    baseUrl: env.BACKEND_URL,
    apiKey: apiKey.key,
  });
};
