{"name": "mysta-agent", "displayName": "<PERSON><PERSON>", "version": "0.2.1", "description": "Mysta - Let AI Run the Web for You.", "manifest": {"permissions": ["storage", "sidePanel", "tabs", "activeTab", "scripting", "debugger"], "host_permissions": ["<all_urls>"], "web_accessible_resources": [{"resources": ["sidepanel.html", "popup.html", "*.js", "*.css"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self' http://localhost; object-src 'self';"}}, "scripts": {"dev": "plasmo dev", "build": "plasmo build --no-minify --source-maps", "watch": "plasmo watch", "package": "plasmo package", "test": "jest"}, "dependencies": {"@heroicons/react": "^2.1.1", "@plasmohq/messaging": "^0.7.1", "@plasmohq/storage": "^1.15.0", "@the-agent/shared": "workspace:*", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/turndown": "^5.0.5", "antd": "^5.25.1", "crypto-js": "^4.2.0", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "dotenv": "^16.0.3", "lucide-react": "^0.509.0", "property-information": "^7.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^8.0.7", "remark-gfm": "^3.0.1", "turndown": "^7.2.0", "uuid": "^9.0.1", "webextension-polyfill": "^0.10.0"}, "devDependencies": {"@types/chrome": "^0.0.248", "@types/crypto-js": "^4.2.2", "@types/jest": "^29.5.14", "@types/jsdom": "^21.1.7", "@types/node": "^20.11.0", "@types/uuid": "^9.0.7", "@types/webextension-polyfill": "^0.10.7", "autoprefixer": "^10.4.21", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jsdom": "^23.2.0", "plasmo": "^0.90.3", "postcss": "^8.5.3", "ts-jest": "^29.3.4", "typescript": "^5.3.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "transform": {"^.+\\.tsx?$": ["ts-jest", {"tsconfig": "tsconfig.json"}]}, "testRegex": "(/__tests__/.*|(\\.|/)(test|spec))\\.(jsx?|tsx?)$", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}}