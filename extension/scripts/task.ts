import 'dotenv/config';

import { ChatAgent, ChatOptions, Message, Task, ToolCall, ToolCallResult } from '@the-agent/shared';
import { GlobalContext, TaskWithState } from '~/types/task';
import { run } from '~/agents/runner';
import { openai, MODEL_GERMINI_PRO } from './common';

import { createSimpleAgent } from '~/agents/simple';
import { createForeachAgent } from '~/agents/foreach';

import { TaskContextBuilder } from '~/agents/context';
import { taskTools } from '~/tools/task-toolkit';
import { DEFAULT_MAX_TOOL_CALLS } from '~/configs/common';
import { TaskToolExecutor } from '~/tools/task-executor';
import { ToolDescription } from '~/types';
import { initTaskState } from '~/agents/runner';

const root: Task = {
  id: 'root',
  goal: "I'd like to know the activities of top 10 web3 kols in twitter",
  output: 'output their twitter handle and what they are talking about in a json array',
};

class DummyToolExecutor extends TaskToolExecutor {
  constructor(c: GlobalContext) {
    super(c);
  }

  async executeInternal(
    _task: TaskWithState,
    toolName: string,
    _params: any
  ): Promise<ToolCallResult> {
    throw new Error(`Unsupported tool call ${toolName}`);
  }

  getToolDescriptions(): ToolDescription[] {
    return taskTools;
  }

  getPostToolcallMessageInternal(toolCall: ToolCall): string {
    return '';
  }
}

const chatOptions: ChatOptions = {
  conversationId: 1,
  onMessageUpdate: async () => {},
  onMessageComplete: async (message: Message, printTokenUsage: boolean = false) => {
    const prefix = `[Task: ${message.task_id}][Agent: ${message.agent_id}]`;
    if (message.role === 'user') {
      console.log(`\n${prefix}[User] (content): ${message.content}`);
    } else if (message.role === 'assistant') {
      if (message.content) {
        console.log(`\n${prefix}[Assistant] (content): ${message.content}`);
      }
      if (printTokenUsage) {
        console.log(
          `\n${prefix}[Assistant] (usage): ${JSON.stringify(message.token_usage, null, 2)}`
        );
      }
      if (message.tool_calls && message.tool_calls.length > 0) {
        console.log(
          `\n${prefix}[Assistant] (tools): ${JSON.stringify(message.tool_calls, null, 2)}`
        );
      }
    } else if (message.role === 'tool') {
      console.log(`\n${prefix}[Tool] (content): ${message.content}`);
    }
  },
};

const context: GlobalContext = {
  chatOptions,
  processing: [],
  processed: [],
  agents: {},
  workflowId: 'workflow_' + Date.now(),
  tasks: {},
  aborted: false,
};

const FAKE_AGENT_SYSTEM_PROMPT = `
You are an chat bot pretending to be a browser controller.
If user asks for something, you simply return some faked data to them and pretend you have done the task.

You will be provided with the following information:
- **Task history memory**: A summary of what has been done so far.
- **Site-specific Q&A Knowledge**: Relevant knowledge base for the current action, formatted as Q: question / A: answer pairs.
- **Current URL**: The URL of the active browser tab.

--

## 📥 Task Result Finalization

- You must call \`TaskToolkit_storeResult\` to save the final output of the task.

### ✅ Stopping Rule:

After calling \`TaskToolkit_storeResult\`, you must:
- **Stop execution immediately**
- Do **not** call any further tools
- Do **not** print or summarize the output, simply respond with "Task completed." or "Task failed."

You must NEVER call \`TaskToolkit_storeResult\` more than once for the same task. Doing so is an error.
-- 

## ❌ Common Mistakes to Avoid

- Do not call any tools after a successful result has been stored
- Do not print the output again after calling \`TaskToolkit_storeResult\`
`;

const fakeAgent = new ChatAgent({
  id: 'fake',
  llmClient: openai,
  model: MODEL_GERMINI_PRO,
  systemPrompt: FAKE_AGENT_SYSTEM_PROMPT,
  contextBuilder: new TaskContextBuilder(context),
  maxToolCalls: DEFAULT_MAX_TOOL_CALLS,
  toolExecutor: new DummyToolExecutor(context),
});

async function main() {
  const browser = fakeAgent;
  context.agents.simple = createSimpleAgent(MODEL_GERMINI_PRO, openai, browser, context);
  context.agents.foreach = createForeachAgent(MODEL_GERMINI_PRO, openai, context);

  const rootWithState = await initTaskState(context, root);
  await run(rootWithState, context);

  console.log(`Task ${rootWithState.task.id}: `);
  console.log(`    Goal: ${rootWithState.task.goal}`);
  console.log(`    Result: ${JSON.stringify(rootWithState.state.results)}`);
}

main().then(() => {
  console.log('Done');
});
