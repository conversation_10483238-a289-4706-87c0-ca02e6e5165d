# 环境变量
.env*
!.env.example

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
!tg-webapp/src/lib/
!web/src/lib/
!landing/lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
venv/
.venv/

# Node
node_modules/
.next/
out/
.cache/
.nuxt/
.yarn/

# 日志
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 系统文件
.DS_Store
Thumbs.db
.idea/
.vscode/
*.swp
*.swo 

extension/.plasmo*
extension/build
extension/node_modules
backend/node_modules
web/node_modules

# prisma generated files
/web/src/generated/prisma

# wrangler
.wrangler
.dev.vars
wrangler.prod.toml

# cursor
.cursor/

# tg bot
tg-bot/bot-state.json
