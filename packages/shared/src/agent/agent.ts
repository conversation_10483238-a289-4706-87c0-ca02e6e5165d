import { ChatCompletionMessageParam } from 'openai/resources/chat';
import { Agent, AgentConfig, AgentRuntimeConfig, ChatOptions, ChatStatus } from '../types/agent';
import { ChatMessage, Message } from '../types/api';

export class ChatAgent implements Agent {
  private config: AgentConfig;
  private status: ChatStatus = 'idle';
  private abortController: AbortController | null = null;

  constructor(config: AgentConfig) {
    this.config = config;
    this.status = 'idle';
  }

  getConfig(): AgentConfig {
    return this.config;
  }

  async abort() {
    this.abortController?.abort();
  }

  async run(userMessage: Message, runtimeConfig: AgentRuntimeConfig = {}): Promise<Message[]> {
    const chatOptions = runtimeConfig.chatOptions;
    await this.updateStatus('running', chatOptions);
    const inputMessages = await this.config.contextBuilder.build(userMessage);
    await chatOptions?.onMessageComplete?.(userMessage);
    let toolCallCount = 0;
    while (this.status === 'running') {
      const assistantMsg = await this.inferOnce(inputMessages, chatOptions);
      await chatOptions?.onMessageComplete?.(assistantMsg);
      inputMessages.push(assistantMsg);

      if (
        !this.config.toolExecutor ||
        !assistantMsg.tool_calls ||
        assistantMsg.status === 'error'
      ) {
        break;
      }

      toolCallCount += assistantMsg.tool_calls?.length || 0;
      if (toolCallCount > this.config.maxToolCalls) {
        // let AI agent to decide if to finish the task
        const nextMessage: Message = buildMaxToolcallExceedMessage(userMessage);
        toolCallCount = toolCallCount - 1;
        await chatOptions?.onMessageComplete?.(nextMessage);
        inputMessages.push(nextMessage);
        continue;
      }

      for (const toolCall of assistantMsg.tool_calls) {
        if (this.status !== 'running') {
          break;
        }
        const toolMessage: Message = {
          id: Date.now(),
          conversation_id: assistantMsg.conversation_id,
          content: '',
          role: 'tool',
          name: toolCall.function.name,
          tool_call_arguments: toolCall.function.arguments,
          tool_call_result: null,
          tool_call_id: toolCall.id,
          task_id: userMessage.task_id,
          agent_id: this.config.id,
          run_id: userMessage.run_id,
          status: 'pending',
        };
        await chatOptions?.onMessageUpdate?.(toolMessage);
        await this.updateStatus('calling_tool', chatOptions);
        try {
          toolMessage.tool_call_result = toolCall.result = await this.config.toolExecutor.execute(
            toolCall,
            {
              toolOptions: {
                ...runtimeConfig.toolOptions,
                onToolMessageUpdate: async (result: any) => {
                  toolMessage.tool_call_result = result;
                  await chatOptions?.onMessageUpdate?.(toolMessage);
                },
              },
              task_id: assistantMsg.task_id ?? undefined,
            }
          );
        } catch (error) {
          toolMessage.tool_call_result = toolCall.result = {
            success: false,
            error: error instanceof Error ? error.message : 'Failed to execute tool',
          };
          toolMessage.status = 'error';
          toolMessage.error = error instanceof Error ? error.message : 'Failed to execute tool';
        }
        await this.updateStatus('running', chatOptions);
        toolMessage.content = JSON.stringify(toolMessage.tool_call_result);
        await chatOptions?.onMessageComplete?.(toolMessage);
        inputMessages.push(toolMessage);
      }

      if (assistantMsg.tool_calls && assistantMsg.tool_calls.length > 0) {
        const lastToolCall = assistantMsg.tool_calls[assistantMsg.tool_calls.length - 1];
        if (this.config.toolExecutor?.isFinalToolCall?.(lastToolCall)) {
          break;
        }
        const content = this.config.toolExecutor?.getPostToolcallMessage(lastToolCall);
        if (content) {
          const postToolcallMessage = buildPostToolcallMessage(assistantMsg, content);
          await chatOptions?.onMessageComplete?.(postToolcallMessage);
          inputMessages.push(postToolcallMessage);
        }
      }
    }
    await this.updateStatus('idle', chatOptions);

    await chatOptions?.onChatComplete?.(inputMessages);

    return inputMessages;
  }

  async inferOnce(inputMessages: Message[], chatOptions?: ChatOptions): Promise<Message> {
    this.abortController = new AbortController();

    const stream = this.config.llmClient.chat.completions.stream(
      {
        model: this.config.model,
        messages: this.prepareMessages(inputMessages),
        tools: this.config.toolExecutor?.getTools() || [],
        tool_choice: 'auto',
      },
      {
        signal: this.abortController.signal,
      }
    );
    const message: Message = {
      id: Date.now(),
      conversation_id: chatOptions?.conversationId,
      role: 'assistant',
      content: '',
      reasoning: '',
      status: 'pending',
      task_id: inputMessages[0].task_id,
      agent_id: this.config.id,
      run_id: inputMessages[0].run_id,
    } as Message;
    // if abort is called, the stream will be aborted
    try {
      for await (const chunk of stream) {
        const delta = chunk.choices[0]?.delta as { reasoning?: string; content?: string };
        if (delta && (delta.content || delta.reasoning)) {
          message.reasoning += delta.reasoning || '';
          message.content += delta.content || '';
          await chatOptions?.onMessageUpdate?.(message);
          await this.updateStatus('streaming', chatOptions);
        }
      }
      await this.updateStatus('running', chatOptions);
      const resp = await stream.finalChatCompletion();
      message.token_usage = resp.usage;
      message.tool_calls = resp.choices[0].message.tool_calls;
    } catch (error) {
      console.error('ChatAgent inferOnce - Error:', error);

      message.status = 'error';
      if (this.abortController?.signal.aborted) {
        message.error = 'Request aborted';
      } else {
        message.error = error instanceof Error ? error.message : 'Something went wrong';
      }
      await this.updateStatus('idle', chatOptions);
    }
    await chatOptions?.onMessageUpdate?.(message);
    return message;
  }

  private async updateStatus(status: ChatStatus, chatOptions?: ChatOptions) {
    this.status = status;
    await chatOptions?.onStatusChange?.(this.status);
  }

  private prepareMessages(messages: Message[]): ChatCompletionMessageParam[] {
    const filtered: ChatMessage[] = messages.map(m => {
      return {
        role: m.role,
        content: m.content,
        name: m.name,
        tool_calls: m.tool_calls,
        tool_call_id: m.tool_call_id,
      };
    });
    if (filtered.length === 0) {
      throw new Error('No messages provided');
    }
    if (filtered[0].role !== 'system' && this.config.systemPrompt) {
      filtered.unshift({
        role: 'system',
        content: this.config.systemPrompt,
      });
    }
    return filtered as ChatCompletionMessageParam[];
  }
}

function buildMaxToolcallExceedMessage(userMessage: Message): Message {
  return {
    id: Date.now(),
    conversation_id: userMessage.conversation_id,
    role: 'user',
    content: `
You are requesting to run a tool call but we are hitting the limit of tool calls. You cannot call any more tools from now on.
You should:
1. complete the task with the TaskToolkit tools if it's provided
2. stop immediately if TaskToolkit_storeResult tool is not provided or already called`,
    status: 'pending',
    task_id: userMessage.task_id,
    agent_id: userMessage.agent_id,
    run_id: userMessage.run_id,
    actor: 'system',
  };
}

function buildPostToolcallMessage(message: Message, content: string): Message {
  return {
    id: Date.now(),
    conversation_id: message.conversation_id,
    role: 'user',
    content,
    status: 'pending',
    task_id: message.task_id,
    agent_id: message.agent_id,
    run_id: message.run_id,
    actor: 'system',
  };
}
