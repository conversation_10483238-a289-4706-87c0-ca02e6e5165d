import { Hono } from 'hono';
import { cors } from 'hono/cors';
import { fromHono } from 'chanfana';

import { jwtAuthMiddleware, jwtOrApiKeyAuthMiddleware } from './auth';
import { GatewayServiceError } from './types/service';

import { SaveMessageV2 } from './handlers/message';
import { CreateConversation, DeleteConversation, ListConversations } from './handlers/conversation';
import { ChatCompletions } from './handlers/chat';
import { corsHeaders } from './utils/common';
import { StripeCheckout, StripeWebhook } from './handlers/stripe';
import {
  GetCreditDaily,
  GetUser,
  GetUserBalance,
  RotateApiKey,
  RedeemCouponCode,
  GenerateCouponCode,
} from './handlers/user';
import { AddMemory, SearchMemory, SearchMemoryV2 } from './handlers/memory';
import { SearchSiteMessage } from './handlers/site';

const app = new Hono<{ Bindings: Env }>();

// CORS middleware
app.use(
  '*',
  cors({
    origin: '*',
    allowMethods: ['GET', 'POST', 'OPTIONS'],
    allowHeaders: ['Content-Type', 'Authorization', 'X-API-Key'],
    exposeHeaders: ['*'],
    maxAge: 86400, // 24 hours
  })
);

// Add unauthenticated routes
app.get('/', c => c.text(''));
app.get('/health', c => c.json({ status: 'OK', version: '0.0.1' }, 200, corsHeaders));

// Authenticated routes
app.use('/v1/conversation/create', jwtOrApiKeyAuthMiddleware);
app.use('/v1/conversation/delete', jwtOrApiKeyAuthMiddleware);
app.use('/v1/conversation/list', jwtOrApiKeyAuthMiddleware);

app.use('/v1/chat/completions', jwtOrApiKeyAuthMiddleware);
app.use('/v2/message/save', jwtOrApiKeyAuthMiddleware);

app.use('/v1/message/search_site', jwtOrApiKeyAuthMiddleware);

app.use('/v1/memory/search', jwtOrApiKeyAuthMiddleware);
app.use('/v1/memory/add', jwtOrApiKeyAuthMiddleware);
app.use('/v2/memory/search', jwtOrApiKeyAuthMiddleware);

app.use('/v1/user/balance', jwtOrApiKeyAuthMiddleware);
app.use('/v1/user/credit_daily', jwtOrApiKeyAuthMiddleware);
app.use('/v1/user', jwtOrApiKeyAuthMiddleware);

// Only JWT auth
app.use('/v1/user/rotate_api_key', jwtAuthMiddleware);
app.use('/v1/user/redeem_coupon_code', jwtAuthMiddleware);
app.use('/v1/stripe/checkout', jwtAuthMiddleware);

app.onError(async (err, c) => {
  if (err instanceof GatewayServiceError) {
    return c.text(err.message, err.code);
  }
  console.error(err);
  return c.text('Internal Server Error', 500);
});

const openapi = fromHono(app, {
  schema: {
    info: {
      title: 'Mizu Node Gateway',
      version: '0.0.1',
      description: 'API Gateway for Mizu AI Agent',
    },
    security: [
      {
        BearerAuth: [],
      },
    ],
  },
});

openapi.registry.registerComponent('securitySchemes', 'BearerAuth', {
  type: 'http',
  scheme: 'bearer',
});

// agent core endpoints
openapi.post('/v1/conversation/create', CreateConversation);
openapi.post('/v1/conversation/delete', DeleteConversation);
openapi.get('/v1/conversation/list', ListConversations);

openapi.post('/v2/message/save', SaveMessageV2);
openapi.post('/v1/message/search_site', SearchSiteMessage);

openapi.get('/v1/memory/search', SearchMemory);
openapi.post('/v1/memory/add', AddMemory);

openapi.post('/v2/memory/search', SearchMemoryV2);

// Register chat completion route
openapi.post('/v1/chat/completions', ChatCompletions);

openapi.post('/v1/stripe/checkout', StripeCheckout);
openapi.post('/v1/stripe/webhook', StripeWebhook);

openapi.post('/v1/user/rotate_api_key', RotateApiKey);
openapi.post('/v1/user/redeem_coupon_code', RedeemCouponCode);

// Bot-only endpoints (no auth middleware needed)
openapi.post('/v1/gen_coupon_code', GenerateCouponCode);

openapi.get('/v1/user/balance', GetUserBalance);
openapi.get('/v1/user/credit_daily', GetCreditDaily);
openapi.get('/v1/user', GetUser);

// OpenAPI documentation endpoints
app.get('/docs/openapi.json', c => {
  // @ts-expect-error: openapi is not a type
  const schema = openapi.schema || openapi.getGeneratedSchema?.() || {};
  return c.json(schema);
});

app.get('/docs', c => {
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Mizu API Documentation</title>
  <link rel="stylesheet" href="https://unpkg.com/swagger-ui-dist@5.0.0/swagger-ui.css" />
</head>
<body>
  <div id="swagger-ui"></div>
  <script src="https://unpkg.com/swagger-ui-dist@5.0.0/swagger-ui-bundle.js"></script>
  <script>
    window.onload = () => {
      window.ui = SwaggerUIBundle({
        url: '/docs/openapi.json',
        dom_id: '#swagger-ui',
      });
    };
  </script>
</body>
</html>
  `;
  return c.html(html);
});

export default app;
