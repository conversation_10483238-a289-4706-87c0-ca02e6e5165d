import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { createClient } from '@supabase/supabase-js';

import {
  CreateConversationRequestSchema,
  CreateConversationResponseSchema,
  DeleteConversationRequestSchema,
  DeleteConversationResponseSchema,
  ListConversationsRequestSchema,
  ListConversationsResponseSchema,
} from '@the-agent/shared';

// ===== CREATE CONVERSATION =====

export class CreateConversation extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: CreateConversationRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Conversation created successfully',
        content: {
          'application/json': {
            schema: CreateConversationResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const { id } = await c.req.json();

    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY);
    const { error } = await supabase.from('conversations').insert({
      local_id: id,
      user_id: userId,
      status: 'active',
    });
    if (error) {
      return c.json({ error: error.message }, 500);
    }
    return c.json({ id }, 200);
  }
}

// ===== DELETE CONVERSATION =====

export class DeleteConversation extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: DeleteConversationRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Conversation deleted successfully',
        content: {
          'application/json': {
            schema: DeleteConversationResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const { id } = await c.req.json();

    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY);
    const { error } = await supabase
      .from('conversations')
      .update({ status: 'deleted' })
      .eq('local_id', id)
      .eq('user_id', userId);
    if (error) {
      return c.json({ error: error.message }, 500);
    }

    return c.json({ deleted: true }, 200);
  }
}

// ===== LIST CONVERSATIONS =====

export class ListConversations extends OpenAPIRoute {
  schema = {
    request: {
      query: ListConversationsRequestSchema,
    },
    responses: {
      '200': {
        description: 'List of user conversations',
        content: {
          'application/json': {
            schema: ListConversationsResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const startFrom = parseInt(c.req.query('startFrom') || '0');

    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY);
    const { data, error } = await supabase
      .from('conversations')
      .select('*, messages(*)')
      .eq('user_id', userId)
      .eq('status', 'active')
      .gte('local_id', startFrom);
    if (error) {
      return c.json({ error: error.message }, 500);
    }

    const conversations = data.map(conversation => ({
      id: conversation.local_id,
      messages: conversation.messages.map((message: any) => ({
        id: message.local_id,
        conversation_id: conversation.local_id,
        role: message.role,
        content: message.content,
        reasoning: message.reasoning,
        tool_calls: convertToolCalls(message.tool_calls),
        tool_call_id: message.tool_call_id,
        name: message.name,
        status: message.status,
        error: message.error,
      })),
    }));
    return c.json({ conversations }, 200);
  }
}

function convertToolCalls(tool_calls: unknown) {
  if (typeof tool_calls === 'string') {
    return JSON.parse(tool_calls);
  }
  return tool_calls;
}
