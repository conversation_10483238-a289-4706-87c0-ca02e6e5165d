import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import {
  Message,
  MessageSchema,
  SaveMessageRequestSchemaV2,
  SaveMessageResponseSchemaV2,
} from '@the-agent/shared';
import { GatewayServiceError } from '../types/service';
import { createClient } from '@supabase/supabase-js';

export class SaveMessageV2 extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: SaveMessageRequestSchemaV2,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Message saved successfully with memory server integration',
        content: {
          'application/json': {
            schema: SaveMessageResponseSchemaV2,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const body = await c.req.json();

    // Validate the message
    let message: Message;
    try {
      message = MessageSchema.parse(body.message);
    } catch (error) {
      console.error('Invalid message format:', error);
      throw new GatewayServiceError(400, 'Invalid message format');
    }

    const supabase = createClient(c.env.SUPABASE_URL, c.env.SUPABASE_KEY);
    const { error } = await supabase.rpc('insert_message_with_conversation', {
      payload: {
        p_local_id: message.id,
        p_role: message.role,
        p_content: message.content,
        p_reasoning: message.reasoning,
        p_tool_calls: JSON.stringify(message.tool_calls),
        p_tool_call_id: message.tool_call_id,
        p_name: message.name,
        p_user_id: userId,
        p_conversation_local_id: message.conversation_id,
        p_status: message.status || 'completed',
        p_error: message.error,
        p_actor: message.actor || 'user',
        p_task_id: message.task_id,
        p_run_id: message.run_id,
        p_agent_id: message.agent_id,
      },
    });

    if (error) {
      return c.json({ error: error.message }, 500);
    }

    return c.json({ success: true }, 200);
  }
}
