import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import {
  SearchMemoryOptions,
  SearchSiteMessageRequestSchema,
  SearchSiteMessageResponseSchema,
} from '@the-agent/shared';
import { GatewayServiceError } from '../types/service';

import { extractSiteIdFromUrl, searchMemory } from '../utils/memory';

// Search site message implementation
export class SearchSiteMessage extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: SearchSiteMessageRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Site message searched successfully from global memory',
        content: {
          'application/json': {
            schema: SearchSiteMessageResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const body = await c.req.json();

    // Validate site
    if (!body.site || typeof body.site !== 'string') {
      throw new GatewayServiceError(400, 'Site URL is required');
    }

    // Validate user_message
    if (!body.user_message || typeof body.user_message !== 'string') {
      throw new GatewayServiceError(400, 'User message is required');
    }

    if (body.limit && typeof body.limit !== 'number') {
      throw new GatewayServiceError(400, 'Limit must be a number');
    }
    const limit = body.limit ?? 5;

    try {
      // 1. Search similar questions from memories using vector search
      const hostname = extractSiteIdFromUrl(body.site);
      const searchConfig: SearchMemoryOptions = {
        limit,
        filters: {
          hostname,
        },
      };

      const searchResult = await searchMemory(c.env, body.user_message, searchConfig);

      // If no similar questions found, return empty array
      if (!searchResult.results || searchResult.results.length === 0) {
        return c.json(
          {
            related_site_messages: [],
          },
          200
        );
      }

      // 2. Get question and answer pairs from knowledge_base
      // Filter out null/undefined IDs using type guard
      const faqIds = searchResult.results
        .map(item => item.metadata?.id)
        .filter((id): id is string => id !== undefined && id !== null);

      if (faqIds.length === 0) {
        return c.json(
          {
            related_site_messages: [],
          },
          200
        );
      }

      const placeholders = faqIds.map(() => '?').join(',');

      const stmt = c.env.DB.prepare(
        `SELECT faq_id, question, answer FROM knowledge_base 	
         WHERE faq_id IN (${placeholders})`
      );

      const result = await stmt.bind(...faqIds).all();

      const relatedMessages = result.results.map(
        (row: { faq_id: string; question: string; answer: string }) => ({
          faq_id: row.faq_id,
          question: row.question,
          answer: row.answer,
        })
      );

      return c.json(
        {
          related_site_messages: relatedMessages,
        },
        200
      );
    } catch (error) {
      console.error('Error in SearchSiteMessage:', error);
      throw new GatewayServiceError(500, 'Failed to search site messages');
    }
  }
}
