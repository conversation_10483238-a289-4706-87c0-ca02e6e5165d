{"name": "metadata-migrator", "version": "0.0.0", "private": true, "scripts": {"deploy": "wrangler deploy", "start": "wrangler dev --config wrangler.local.toml --local", "test": "vitest", "cf-typegen": "wrangler types", "type-check": "tsc --noEmit"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250121.0", "@types/node": "^22.10.1", "dotenv": "^16.4.7", "ts-node": "^10.9.2", "tsc-files": "^1.1.4", "typescript": "^5.5.2", "vitest": "2.0.5", "wrangler": "^4.16.0"}, "dependencies": {"@supabase/supabase-js": "^2.39.3", "@the-agent/shared": "workspace:*", "chanfana": "^2.8.0", "hono": "^4.7.9", "jose": "^5.9.6", "stripe": "^18.1.0", "uuid": "^11.1.0"}, "lint-staged": {"*.{js,ts}": ["prettier --write", "eslint --fix", "tsc-files --noEmit"], "*.{json,md}": ["prettier --write"]}}