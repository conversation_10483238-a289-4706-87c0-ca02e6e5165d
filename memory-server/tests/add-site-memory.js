const axios = require('axios');

const siteJson = [
  {
    site: 'https://x.com',
    faqs: [
      {
        question: 'How to scroll down on https://x.com',
        answer:
          'To scroll down the page, use the WebToolkit_sendKeys tool with the Space key. This will scroll down one page at a time. WebToolkit_sendKeys({"keys":"Space"})',
      },
      {
        question: 'How to search for content on https://x.com/home',
        answer: `To search for content, use the WebToolkit_inputElementByKey tool. You'll need the hash key of the search field and the value you want to search for. Set pressEnterAfterInput to true to submit the search. WebToolkit_inputElementByKey({"hashKey":"00cb","value":"?","options":{"pressEnterAfterInput":true}})`,
      },
      {
        question: 'How to write a post on https://x.com/home',
        answer: `To write a post, use the WebToolkit_inputElementByKey tool. You'll need the hash key of the write field and the value you want to post. WebToolkit_inputElementByKey({"hashKey":"427a","value":"?"})`,
      },
      {
        question: 'How to click post on https://x.com/home',
        answer: `To click the post button, use the WebToolkit_clickElementByIndex tool with the hash key of the post button. Must be executed after input. WebToolkit_clickElementByIndex({"hashKey":"7225"})`,
      },
      {
        question: 'How to follow a user on their profile page',
        answer: `To follow a user on their profile page, use the WebToolkit_clickElementByIndex tool with the hash key of the follow button. WebToolkit_clickElementByIndex({"hashKey":"766b"})`,
      },
      {
        question: 'How to write a reply on https://x.com/{user_id}/status/{post_id}',
        answer: `To write a reply to a post, use the WebToolkit_inputElementByKey tool. You'll need the hash key of the reply input field and the value you want to reply with. WebToolkit_inputElementByKey({"hashKey":"ed18","value":"?"})`,
      },
      {
        question: 'How to click reply button on https://x.com/{user_id}/status/{post_id}',
        answer: `To click the reply button, use the WebToolkit_clickElementByIndex tool with the hash key of the reply button. Must be executed after input. WebToolkit_clickElementByIndex({"hashKey":"2340"})`,
      },
      {
        question: 'How to like a post on https://x.com/{user_id}/status/{post_id}',
        answer: `To like a post, use the WebToolkit_clickElementByIndex tool with the hash key of the like button. WebToolkit_clickElementByIndex({"hashKey":"599a"})`,
      },
      {
        question: 'How to bookmark a post on https://x.com/{user_id}/status/{post_id}',
        answer: `To bookmark a post, use the WebToolkit_clickElementByIndex tool with the hash key of the bookmark button. WebToolkit_clickElementByIndex({"hashKey":"af1b"})`,
      },
      {
        question: 'How to find Posts on https://x.com/home',
        answer: `To find Posts, use the WebToolkit_buildElementMap tool. You'll need the fullXPath of the comment field. WebToolkit_buildElementMap({"fullXPath":"/html/body/div[1]/div/div/div[2]/main/div/div/div/div[1]/div/div[5]/section/div"})`,
      },
      {
        question: 'How to find Comments on https://x.com/{user_id}/status/{post_id}',
        answer: `To find Comments, use the WebToolkit_buildElementMap tool. You'll need the fullXPath of the comment field. WebToolkit_buildElementMap({"fullXPath":"/html/body/div[1]/div/div/div[2]/main/div/div/div/div[1]/div/section/div"})`,
      },
      {
        question: 'How to find Messages on https://x.com/messages',
        answer: `To find Messages, use the WebToolkit_buildElementMap tool. You'll need the fullXPath of the comment field. WebToolkit_buildElementMap({"fullXPath":"/html/body/div[1]/div/div/div[2]/main/div/div/div/section[1]/div/div/div[2]/section/div"})`,
      },
    ],
  },
  {
    site: 'https://www.xiaohongshu.com/',
    faqs: [
      {
        question: 'How to scroll down on https://www.xiaohongshu.com',
        answer: `To scroll down the page, use the WebToolkit_sendKeys tool with the Space key to scroll down one screen. WebToolkit_sendKeys({"keys":"Space"})`,
      },
      {
        question: 'How to search for content on https://www.xiaohongshu.com/explore',
        answer: `To search for content, use the WebToolkit_inputElementByKey tool. You'll need the hash key of the search field and the value you want to search for. Set pressEnterAfterInput to true to submit the search. WebToolkit_inputElementByKey({"hashKey":"8f16","value":"?","options":{"pressEnterAfterInput":true}})`,
      },
      {
        question:
          'How to view user profile from post detail page on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To view the user profile from a post detail page, use the WebToolkit_clickElementByIndex tool with the hash key of the profile button. WebToolkit_clickElementByIndex({"hashKey":"099f"})`,
      },
      {
        question: 'How to follow a user on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To follow a user on the post detail page, use the WebToolkit_clickElementByIndex tool with the hash key of the follow button. WebToolkit_clickElementByIndex({"hashKey":"9083"})`,
      },
      {
        question: 'How to close post detail page on https://www.xiaohongshu.com/explore/{post_id}',
        answer:
          'To close the post detail page, use the WebToolkit_clickElementByIndex tool with the hash key of the close button. WebToolkit_clickElementByIndex({"hashKey":"78c4"})',
      },
      {
        question: 'How to write a reply on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To write a reply to a post, use the WebToolkit_inputElementByKey tool. You'll need the hash key of the reply input field and the value you want to reply with. WebToolkit_inputElementByKey({"hashKey":"8e1e","value":"?"})`,
      },
      {
        question: 'How to submit reply on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To submit the reply, use the WebToolkit_clickElementByIndex tool with the hash key of the reply button. Must be executed after input. WebToolkit_clickElementByIndex({"hashKey":"5888"})`,
      },
      {
        question: 'How to like a post on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To like a post, use the WebToolkit_clickElementByIndex tool with the hash key of the like button. WebToolkit_clickElementByIndex({"hashKey":"9441"})`,
      },
      {
        question: 'How to collect a post on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To collect a post, use the WebToolkit_clickElementByIndex tool with the hash key of the collect button. WebToolkit_clickElementByIndex({"hashKey":"d0da"})`,
      },
      {
        question: 'How to view comments on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To view comments on a post, use the WebToolkit_clickElementByIndex tool with the hash key of the view comment button. WebToolkit_clickElementByIndex({"hashKey":"8407"})`,
      },
      {
        question: 'How to find Posts on https://www.xiaohongshu.com/explore',
        answer: `To find Posts, use the WebToolkit_buildElementMap tool. You'll need the fullXPath of the comment field. WebToolkit_buildElementMap({"fullXPath":"/html/body/div[2]/div[1]/div[2]/div[2]/div/div[3]"})`,
      },
      {
        question: 'How to find Comments on https://www.xiaohongshu.com/explore/{post_id}',
        answer: `To find Comments, use the WebToolkit_buildElementMap tool. You'll need the fullXPath of the comment field. WebToolkit_buildElementMap({"fullXPath":"/html/body/div[6]/div[1]/div[4]/div[2]/div[3]/div"})`,
      },
      {
        question: 'How to find Notifications on https://www.xiaohongshu.com/notification',
        answer: `To find Notifications, use the WebToolkit_buildElementMap tool. You'll need the fullXPath of the comment field. WebToolkit_buildElementMap({"fullXPath":"/html/body/div[2]/div[1]/div[2]/div[2]/div[1]/div[2]"})`,
      },
    ],
  },
];

// 配置
const MEMORY_SERVER_URL = 'http://localhost:3000/memory';
const BATCH_SIZE = 10; // 每批处理的数量
const DELAY_BETWEEN_BATCHES = 1000; // 批次间延迟（毫秒）

// 数据结构定义
const metadataStructure = {
  hostname: '', // 网站域名
  question: '', // 问题
  answer: '', // 答案
  created_at: '', // 创建时间
  memoryType: 'site', // 内存类型
  updated_at: '', // 更新时间
};

// 转换数据格式
function transformData(siteData) {
  const memories = [];

  siteData.forEach(site => {
    // 确保 URL 有正确的协议
    let siteUrl = site.site;
    if (!siteUrl.startsWith('http://') && !siteUrl.startsWith('https://')) {
      siteUrl = 'https://' + siteUrl;
    }

    const hostname = new URL(siteUrl).hostname;

    site.faqs.forEach(faq => {
      const memory = {
        data: `Q: ${faq.question}\n\nA: ${faq.answer}`,
        metadata: {
          site: siteUrl,
          hostname: hostname,
          memoryType: 'site',
        },
      };
      memories.push(memory);
    });
  });

  return memories;
}

// 发送单个 memory 请求
async function addMemory(memoryData) {
  try {
    console.log('📤 Sending raw memory:', JSON.stringify(memoryData, null, 2));

    const response = await axios.post(`${MEMORY_SERVER_URL}/add_raw`, memoryData);
    console.log('✅ Memory added successfully:', response.data);
    return true;
  } catch (error) {
    console.error('❌ Failed to add memory:', error.message);
    if (error.response) {
      console.error('Response status:', error.response.status);
      console.error('Response data:', error.response.data);
    }
    return false;
  }
}

// 批量处理
async function processBatch(memories, startIndex) {
  const batch = memories.slice(startIndex, startIndex + BATCH_SIZE);
  console.log(
    `\n📦 Processing batch ${Math.floor(startIndex / BATCH_SIZE) + 1} (${batch.length} items)`
  );

  const promises = batch.map((memory, index) => {
    const questionText = memory.data.split('\n')[0].replace('Q: ', '');
    console.log(
      `  ${startIndex + index + 1}. Adding memory for: ${memory.metadata.hostname} - ${questionText.substring(0, 50)}...`
    );
    return addMemory(memory);
  });

  const results = await Promise.all(promises);
  const successCount = results.filter(result => result).length;
  const failCount = results.length - successCount;

  console.log(`  ✅ Success: ${successCount}, ❌ Failed: ${failCount}`);
  return { successCount, failCount };
}

// 主函数
async function main() {
  console.log('🚀 Starting to add site memories...');
  console.log(`📊 Total sites: ${siteJson.length}`);

  const totalFaqs = siteJson.reduce((sum, site) => sum + site.faqs.length, 0);
  console.log(`📊 Total FAQs: ${totalFaqs}`);

  // 转换数据
  const memories = transformData(siteJson);
  console.log(`🔄 Transformed ${memories.length} memories`);

  let totalSuccess = 0;
  let totalFailed = 0;

  // 分批处理
  for (let i = 0; i < memories.length; i += BATCH_SIZE) {
    const { successCount, failCount } = await processBatch(memories, i);
    totalSuccess += successCount;
    totalFailed += failCount;

    // 如果不是最后一批，添加延迟
    if (i + BATCH_SIZE < memories.length) {
      console.log(`⏳ Waiting ${DELAY_BETWEEN_BATCHES}ms before next batch...`);
      await new Promise(resolve => setTimeout(resolve, DELAY_BETWEEN_BATCHES));
    }
  }

  console.log('\n🎉 Import completed!');
  console.log(`📊 Summary:`);
  console.log(`  ✅ Total successful: ${totalSuccess}`);
  console.log(`  ❌ Total failed: ${totalFailed}`);
  console.log(`  📈 Success rate: ${((totalSuccess / memories.length) * 100).toFixed(2)}%`);
}

// 运行脚本
main().catch(console.error);
